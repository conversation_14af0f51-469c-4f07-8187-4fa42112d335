use serde::{ Deserialize, Serialize };

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct EmailMessage {
    pub id: Option<String>,
    pub thread_id: Option<String>,
    pub from: String,
    pub to: Option<String>,
    pub cc: Option<String>,
    pub bcc: Option<String>,
    pub date: String, // Or chrono::DateTime if you want a Date type
    pub subject: Option<String>,
    pub snippet: Option<String>,
    pub body: String,
    pub attachments: Option<Vec<String>>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct ReplyWithTone {
    pub value: String,
    pub tone: String,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct ReplyAiResponse {
    pub messages: Vec<ReplyWithTone>,
}
