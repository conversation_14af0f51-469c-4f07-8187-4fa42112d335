<template>
  <div class="flex items-start space-x-3 mb-4 h-auto">
    <!-- Avatar -->
    <div
      class="w-12 h-12 rounded-full bg-gradient-to-br from-secondary-300 to-secondary-400 text-primary-100 text-sm font-semibold flex items-center justify-center shadow-md border-2 border-primary-200"
      :title="message.from"
    >
      {{ getInitials(message.from) }}
    </div>

    <!-- Message Content -->
    <div class="flex-1 bg-primary-100 rounded-xl shadow-sm border border-primary-200 overflow-hiddenx h-auto">
      <!-- Message Header -->
      <div class="px-4 py-3 bg-primary-50 border-b border-primary-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <span class="font-semibold text-secondary-800 text-base">
              {{ getSenderName(message.from) }}
            </span>
            <button
              @click="toggleDetails"
              class="text-xs text-secondary-600 hover:text-secondary-800 hover:underline transition-colors duration-200 px-2 py-1 rounded-md hover:bg-primary-200"
            >
              {{ showDetails ? "Hide details" : "Show details" }}
            </button>
          </div>
          <div class="text-xs text-secondary-500 font-medium">
            {{ formatDate(message.date) }}
          </div>
        </div>

        <!-- Detailed Information -->
        <div v-if="showDetails" class="mt-3 space-y-2 text-sm border-t border-primary-200 pt-3">
          <div class="flex gap-3">
            <span class="font-medium text-secondary-700 min-w-12">From:</span>
            <span class="text-secondary-600">{{ getSenderEmail(message.from) }}</span>
          </div>
          <div v-if="message.to" class="flex gap-3">
            <span class="font-medium text-secondary-700 min-w-12">To:</span>
            <span
              @click="toggleToExpanded"
              class="text-secondary-600 cursor-pointer hover:text-secondary-800 transition-colors"
            >
              {{ getDisplayRecipients(message.to, showToExpanded) }}
            </span>
          </div>
          <div v-if="message.cc" class="flex gap-3">
            <span class="font-medium text-secondary-700 min-w-12">Cc:</span>
            <span
              @click="toggleCcExpanded"
              class="text-secondary-600 cursor-pointer hover:text-secondary-800 transition-colors"
            >
              {{ getDisplayRecipients(message.cc, showCcExpanded) }}
            </span>
          </div>
          <div v-if="message.bcc" class="flex gap-3">
            <span class="font-medium text-secondary-700 min-w-12">Bcc:</span>
            <span
              @click="toggleBccExpanded"
              class="text-secondary-600 cursor-pointer hover:text-secondary-800 transition-colors"
            >
              {{ getDisplayRecipients(message.bcc, showBccExpanded) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Message Body -->
      <div class="px-4 py-4 text-secondary-800 leading-relaxed" v-html="message.body"></div>

      <!-- Attachments Section -->
      <div v-if="attachments && attachments.length > 0" class="px-4 pb-4">
        <div class="bg-primary-50 border border-primary-200 rounded-lg p-3">
          <h4 class="text-sm font-semibold text-secondary-700 mb-2 flex items-center gap-2">
            <PaperClipIcon class="w-4 h-4" />
            Attachments ({{ attachments.length }})
          </h4>

          <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
            <div
              v-for="(attachment, index) in attachments"
              :key="index"
              class="flex items-center gap-2 p-2 bg-primary-100 rounded-md border border-primary-200 hover:bg-primary-200 transition-colors duration-200 cursor-pointer group"
              @click="downloadAttachment(attachment)"
            >
              <!-- File Icon -->
              <div class="flex-shrink-0 w-8 h-8 rounded-md bg-secondary-200 flex items-center justify-center">
                <i :class="getFileIconClass(attachment)" class="text-secondary-600 text-sm"></i>
              </div>

              <!-- File Info -->
              <div class="flex-1 min-w-0">
                <div class="text-xs font-medium text-secondary-800 truncate" :title="attachment">
                  {{ getFileName(attachment) }}
                </div>
                <div class="text-xs text-secondary-500">
                  {{ getFileExtension(attachment).toUpperCase() }}
                </div>
              </div>

              <!-- Download Icon -->
              <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <ArrowDownTrayIcon class="w-3 h-3 text-secondary-600" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="flex gap-1 border-y border-primary-500 p-2 overflow-x-auto"
        v-if="props.last && emailSummaries?.recommended_actions && emailSummaries?.recommended_actions.length > 0"
      >
        <div
          v-for="action in emailSummaries?.recommended_actions"
          :key="action"
          @click="generateReplyFromAction(action)"
          class="flex items-center cursor-pointer gap-1 text-[10px] bg-primary-200 hover:bg-primary-300 text-secondary-700 px-3 py-1.5 rounded-md transition-colors duration-200 font-medium border border-primary-300"
          :class="{ 'bg-primary-500': actionIsLoading === action }"
        >
          <SpinnerIcon v-if="actionIsLoading === action" class="size-6" />
          <PencilIcon v-else class="size-6" />
          <span>{{ action }}</span>
          <button
            class="size-6 rounded hover:text-red-700 hover:bg-red-100 flex justify-center items-center transition-colors duration-200"
            v-if="selectedAction === action"
            @click.stop="
              selectedAction = null;
              generatedReplys = null;
            "
          >
            <TrashIcon class="size-3" />
          </button>
        </div>
      </div>

      <div
        class="flex flex-col gap-2 px-4 pt-2 max-h-60x relative"
        :class="{ hidden: (!showReplyEditor || !props.last) && !generatedReplys?.messages }"
      >
        <div
          v-if="generatedReplys?.messages && generatedReplys.messages.length > 0"
          class="flex justify-center items-center gap-2 overflow-y-auto text-xs"
        >
          <button
            @click="selectedReplyIndex = (selectedReplyIndex! - 1) % generatedReplys.messages.length"
            class="size-6 rounded hover:text-primary-200 hover:bg-primary-600 flex justify-center items-center transition-colors duration-200"
          >
            <ChevronLeftIcon class="size-4" />
          </button>
          <p>{{ generatedReplys?.messages[selectedReplyIndex!].tone }}</p>
          <button
            @click="selectedReplyIndex = (selectedReplyIndex! + 1) % generatedReplys.messages.length"
            class="size-6 rounded hover:text-primary-200 hover:bg-primary-600 flex justify-center items-center transition-colors duration-200"
          >
            <ChevronRightIcon class="size-4" />
          </button>
        </div>
        <EmailEditor v-model="replyMessage" />
        <div class="flex flex-wrap gap-2 justify-center items-center">
          <div
            v-for="(attachment, index) in replyAttachments"
            :key="index"
            class="flex items-center gap-2 p-2 bg-primary-100 rounded-md border border-primary-200 hover:bg-primary-200 transition-colors duration-200 cursor-pointer group"
            @click="downloadAttachment(attachment)"
          >
            <!-- File Icon -->
            <div class="size-6 rounded-md bg-secondary-100 flex items-center justify-center">
              <i :class="getFileIconClass(attachment)" class="text-secondary-600 text-xs"></i>
            </div>

            <!-- File Info -->
            <div class="flex-1 min-w-0">
              <div class="text-xs font-medium text-secondary-800 truncate" :title="attachment">
                {{ getFileName(attachment) }}
              </div>
              <div class="text-[8px] text-secondary-500">
                {{ getFileExtension(attachment).toUpperCase() }}
              </div>
            </div>

            <!-- Download Icon -->
            <button
              @click="removeAttachment(attachment)"
              class="hover:bg-red-100 size-5 flex justify-center items-center rounded-md group-hover:opacity-100 transition-opacity duration-200"
            >
              <XMarkIcon class="size-4 text-secondary-600 hover:text-red-600" />
            </button>
          </div>
        </div>
      </div>
      <!-- Actions Section -->
      <div class="border-t border-primary-200 bg-primary-50 px-4 py-3">
        <div class="flex items-center justify-between">
          <!-- Primary Actions -->
          <div class="flex gap-2">
            <button
              v-if="props.last"
              :disabled="replyState !== 'none'"
              @click="showReplyEditor ? handleReply() : (showReplyEditor = true)"
              class="flex items-center gap-1 bg-secondary-400 disabled:bg-secondary-100 hover:bg-secondary-500 disabled:text-secondary-600 disabled:cursor-not-allowed text-primary-100 px-3 py-1.5 rounded-md transition-colors duration-200 font-medium shadow-sm text-sm"
            >
              <div class="flex items-center gap-1" v-if="replyState === 'none'">
                <i class="pi pi-reply text-xs"></i>
                <span v-if="!showReplyEditor">Reply</span>
                <span v-else>Send</span>
              </div>
              <div class="flex justify-center items-center gap-1" v-if="replyState === 'sending'">
                <SpinnerIcon class="w-4 h-4" />
                <span>Sending...</span>
              </div>
              <div class="flex justify-center items-center gap-1" v-if="replyState === 'sent'">
                <CheckBadgeIcon class="size-5" />
                <span>Sent</span>
              </div>
            </button>
            <button
              v-if="showReplyEditor && props.last"
              @click="openFiles"
              class="size-8 bg-dark-300 flex justify-center items-center rounded-md hover:text-red-600 hover:bg-red-100 text-dark-100 transition-all duration-200"
            >
              <PaperClipIcon class="size-5" />
            </button>
            <button
              v-if="showReplyEditor && props.last"
              @click="showReplyEditor = false"
              class="size-8 bg-primary-400 flex justify-center items-center rounded-md hover:text-red-600 hover:bg-red-100 text-primary-800 transition-all duration-200"
            >
              <XMarkIcon class="size-5" />
            </button>

            <!-- <button
              @click="$emit('replyAll', message)"
              class="flex items-center gap-1 bg-primary-200 hover:bg-primary-300 text-secondary-700 px-3 py-1.5 rounded-md transition-colors duration-200 font-medium border border-primary-300 text-sm"
            >
              <i class="pi pi-reply-all text-xs"></i>
              <span>Reply All</span>
            </button> -->

            <!-- <button
              @click="$emit('forward', message)"
              class="flex items-center gap-1 bg-primary-200 hover:bg-primary-300 text-secondary-700 px-3 py-1.5 rounded-md transition-colors duration-200 font-medium border border-primary-300 text-sm"
            >
              <i class="pi pi-share-alt text-xs"></i>
              <span>Forward</span>
            </button> -->
          </div>

          <!-- Secondary Actions -->
          <div class="flex gap-1">
            <!-- <button
              @click="$emit('star', message)"
              class="flex items-center gap-1 bg-primary-200 hover:bg-primary-300 text-secondary-700 px-2 py-1.5 rounded-md transition-colors duration-200 border border-primary-300 text-sm"
              title="Star Message"
            >
              <i class="pi pi-star text-xs"></i>
            </button> -->

            <!-- <button
              @click="$emit('archive', message)"
              class="flex items-center gap-1 bg-primary-200 hover:bg-primary-300 text-secondary-700 px-2 py-1.5 rounded-md transition-colors duration-200 border border-primary-300 text-sm"
              title="Archive Message"
            >
              <i class="pi pi-archive text-xs"></i>
            </button> -->

            <button
              @click="$emit('delete', message)"
              class="flex items-center gap-1 bg-red-100 hover:bg-red-200 text-red-700 px-2 py-1.5 rounded-md transition-colors duration-200 border border-red-300 text-sm"
              title="Delete Message"
            >
              <i class="pi pi-trash text-xs"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import {
  PaperClipIcon,
  ArrowDownTrayIcon,
  PencilIcon,
  XMarkIcon,
  CheckBadgeIcon,
  TrashIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/vue/24/outline";
import { ScalaRoutesService } from "../../../services/scala-api/routes-service";
import EmailEditor from "../email-editor/EmailEditor.vue";
import { EmailMessage } from "./email.types";
import emailCommands, { ReplyResponse, SendEmail } from "../../../commands/emails";
import SpinnerIcon from "../../icons/SpinnerIcon.vue";
import { open } from "@tauri-apps/plugin-dialog";
import { sep } from "@tauri-apps/api/path";
import { error } from "console";
import { EmailSummaries } from "../../../services/scala-api/dto";

interface Props {
  thread?: EmailMessage[];
  message: EmailMessage;
  last: boolean;
}

const props = defineProps<Props>();

defineEmits<{
  reply: [message: EmailMessage];
  replyAll: [message: EmailMessage];
  forward: [message: EmailMessage];
  star: [message: EmailMessage];
  archive: [message: EmailMessage];
  delete: [message: EmailMessage];
}>();

const showDetails = ref(false);
const showToExpanded = ref(false);
const showCcExpanded = ref(false);
const showBccExpanded = ref(false);

const emailSummaries = ref<EmailSummaries>();
const showReplyEditor = ref(false);

const replyMessage = ref<string>("");
const replyAttachments = ref<string[]>([]);
const replyState = ref<"none" | "sending" | "sent" | "error">("none");

const selectedAction = ref<string | null>();
const generatedReplys = ref<ReplyResponse | null>();
const selectedReplyIndex = ref<number | null>();
const actionIsLoading = ref<string | null>();

watch(selectedReplyIndex, (n) => {
  console.log("Selected reply index changed to:", n);
  if (n != null) replyMessage.value = generatedReplys.value?.messages[n].value ?? "";
  else replyMessage.value = "";
});

// Computed property for attachments
const attachments = computed(() => {
  if (!props.message.attachments) return [];
  return Array.isArray(props.message.attachments) ? props.message.attachments.filter((file) => file.trim() !== "") : [];
});

const toggleDetails = () => {
  showDetails.value = !showDetails.value;
};

const toggleToExpanded = () => {
  showToExpanded.value = !showToExpanded.value;
};

const toggleCcExpanded = () => {
  showCcExpanded.value = !showCcExpanded.value;
};

const toggleBccExpanded = () => {
  showBccExpanded.value = !showBccExpanded.value;
};

const getInitials = (email: string): string => {
  const nameMatch = email.match(/^(.*)<(.*)>$/);
  const name = nameMatch ? nameMatch[1].trim() : email.trim();

  return name
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join("");
};

const getSenderName = (email: string): string => {
  const nameMatch = email.match(/^(.*)<(.*)>$/);
  return nameMatch ? nameMatch[1].trim() : email.split("@")[0];
};

const getSenderEmail = (email: string): string => {
  const emailMatch = email.match(/^(.*)<(.*)>$/);
  return emailMatch ? emailMatch[2].trim() : email;
};

const getDisplayRecipients = (recipients: string, expanded: boolean): string => {
  if (!recipients) return "";

  const recipientList = recipients.split(",").map((r) => r.trim());

  return recipientList
    .map((recipient) => {
      const match = recipient.match(/^(.*)<(.*)>$/);
      if (!match) return recipient;

      const name = match[1].trim();
      const email = match[2].trim();
      return expanded ? email : name;
    })
    .join(", ");
};

const formatDate = (dateInput: string | Date): string => {
  const date = new Date(dateInput);
  return date.toLocaleString("en-US", {
    dateStyle: "medium",
    timeStyle: "short",
  });
};

// Attachment helper functions
const getFileName = (filePath: string): string => {
  const separator = sep();
  return filePath.split(separator).pop() || filePath;
};

const getFileExtension = (fileName: string): string => {
  const extension = fileName.split(".").pop()?.toLowerCase() || "";
  return extension;
};

const getFileIconClass = (fileName: string): string => {
  const extension = getFileExtension(fileName);

  const iconMap: Record<string, string> = {
    // Documents
    pdf: "fa-file-pdf",
    doc: "fa-file-word",
    docx: "fa-file-word",
    txt: "fa-file-text",
    rtf: "fa-file-text",

    // Spreadsheets
    xls: "fa-file-excel",
    xlsx: "fa-file-excel",
    csv: "fa-file-csv",

    // Presentations
    ppt: "fa-file-powerpoint",
    pptx: "fa-file-powerpoint",

    // Images
    jpg: "fa-file-image",
    jpeg: "fa-file-image",
    png: "fa-file-image",
    gif: "fa-file-image",
    bmp: "fa-file-image",
    svg: "fa-file-image",
    webp: "fa-file-image",

    // Videos
    mp4: "fa-file-video",
    avi: "fa-file-video",
    mov: "fa-file-video",
    wmv: "fa-file-video",
    flv: "fa-file-video",
    webm: "fa-file-video",

    // Audio
    mp3: "fa-file-audio",
    wav: "fa-file-audio",
    flac: "fa-file-audio",
    aac: "fa-file-audio",
    ogg: "fa-file-audio",

    // Archives
    zip: "fa-file-zipper",
    rar: "fa-file-zipper",
    "7z": "fa-file-zipper",
    tar: "fa-file-zipper",
    gz: "fa-file-zipper",

    // Code
    js: "fa-file-code",
    ts: "fa-file-code",
    html: "fa-file-code",
    css: "fa-file-code",
    php: "fa-file-code",
    py: "fa-file-code",
    java: "fa-file-code",
    cpp: "fa-file-code",
    c: "fa-file-code",

    // Default
    default: "fa-file",
  };

  return `fa-regular ${iconMap[extension] || iconMap.default}`;
};

const downloadAttachment = (attachment: string) => {
  // Emit event to parent component to handle download
  console.log("Download attachment:", attachment);
  // This would typically trigger a download or open the attachment
};

const removeAttachment = (attachment: string) => {
  replyAttachments.value = replyAttachments.value.filter((a) => a !== attachment);
};

async function openFiles() {
  const selected = await open({
    multiple: true,
    directory: false,
    title: "Email attachments",
  });
  console.log("Selected Files =>", selected);
  if (Array.isArray(selected)) {
    replyAttachments.value = [...new Set([...replyAttachments.value, ...selected])];
    return;
  } else if (selected === null) {
    return;
  } else {
    replyAttachments.value = [...new Set([...replyAttachments.value, selected])];
    return;
  }
}

async function generateReplyFromAction(action: string) {
  actionIsLoading.value = action;
  console.log("Generating reply from action", action);
  console.log("For thread", props.thread);
  emailCommands
    .replyEmailFromAction(props.thread!, action)
    .then((data) => {
      selectedAction.value = action;
      generatedReplys.value = data;
      selectedReplyIndex.value = 0;
      actionIsLoading.value = null;
      console.log("🚀 ~ generateReplyFromAction ~ result:", generatedReplys.value);
    })
    .catch((error) => {
      console.error("Error generating reply:", error);
      actionIsLoading.value = null;
    });
}

async function handleReply() {
  if (replyMessage.value.trim() === "") return;
  replyState.value = "sending";
  const reply: SendEmail = {
    recipient: props.message.from,
    subject: props.message.subject ?? "",
    body: replyMessage.value,
    attachments: replyAttachments.value,
    inReplyTo: props.message.id,
    references: props.message.id,
    threadId: props.message.threadId,
  };
  console.log("🚀 ~ handleReply ~ reply:", reply);
  const result = await emailCommands.sendEmail(reply);
  if (result) {
    replyState.value = "sent";
    showReplyEditor.value = false;
    setTimeout(() => {
      replyState.value = "none";
    }, 2000);
  } else {
    replyState.value = "error";
  }
}

onMounted(async () => {
  console.log("Message =>", props.message);
  if (!props.message.id || !props.last) return;
  emailSummaries.value = await ScalaRoutesService.getEmailSummaries(props.message.id);

  // await ScalaRoutesService.getEmailSummaries(props.message.id!);
});
</script>
