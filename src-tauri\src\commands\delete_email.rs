use crate::google_api_functions::check_and_refresh_token::check_and_refresh_token;
use crate::google_api_functions::check_and_refresh_token_unread::check_and_refresh_token_unread;
use crate::google_api_functions::fetch_emails::fetch_emails;
use crate::google_api_functions::refresh_access_token::refresh_access_token;
use crate::models::app_data::AppData;
use crate::models::user_data::User;
use crate::models::user_data::UserData;
use chrono::{ DateTime, Utc };
use mime_guess::mime;
use reqwest::header::{ Keys, AUTHORIZATION, CONTENT_TYPE }; // Import the necessary headers
use reqwest::Client;
use serde_json::json;
use std::sync::Arc;
use std::time::{ Duration, Instant };
use tauri::{ Manager, RunEvent, WindowEvent };
use tokio::sync::Mutex;
use tokio::sync::MutexGuard;
use tokio::sync::RwLock;
use tracing::{ error, info, warn };

use futures::future::BoxFuture;
use google_cloud_pubsub::client::{ Client as PubSubClient, ClientConfig };
use google_cloud_pubsub::subscription::ReceiveConfig;
use std::error::Error;
use std::path::PathBuf;
use tokio::time::sleep;
use tokio_util::sync::CancellationToken; // To handle async closures
type BoxError = Box<dyn Error + Send + Sync>;

#[tauri::command]
pub async fn delete_email_from_gmail(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>, // Match this with fetch_unread_emails
    email_id: String,
    access_token: String
) -> Result<(), String> {
    // check_and_refresh_token(&app_data)
    //     .await
    //     .map_err(|e| format!("Failed to refresh token: {}", e))?;

    let client = Client::new();
    let mut exprie_in_read: Option<Duration> = None;
    let mut issued_at_read: Option<DateTime<Utc>> = None;
    let mut refresh_token: Option<String> = None;
    let mut access_token: Option<String> = None;

    {
        let app_data_arc = app_data.read().await; // Acquire read lock
        let user_data = app_data_arc.user_data.read().await;

        exprie_in_read = user_data.expire_in.clone(); // Clone if `Duration` is needed later
        issued_at_read = user_data.issued_at.clone();
        refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
        access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
    } // Lock is released here automatically

    // let app_data_arc = app_data.lock().await; // Lock before accessing user_data

    // // let app_data_guard = app_data.lock().unwrap();  // Lock the Mutex

    // let mut user_data = app_data_arc.user_data.lock().await;

    // let mut logged_in = app_data_arc.logged_in.lock().await;

    // let user_data = app_data.user_data.lock().await.clone();

    // The Gmail API endpoint to delete an email
    let url = format!(
        "https://gmail.googleapis.com/gmail/v1/users/me/messages/{}",
        email_id.clone()
    );

    const MAX_RETRIES: u8 = 3; // Number of retries
    const RETRY_DELAY: u64 = 2; // Delay between retries in seconds

    for attempt in 1..=MAX_RETRIES {
        match
            client
                .delete(&url)
                .header(AUTHORIZATION, format!("Bearer {}", access_token.clone().unwrap()))
                .header(CONTENT_TYPE, "application/json")
                .send().await
        {
            Ok(response) => {
                if response.status().is_success() {
                    println!("Successfully deleted email with ID: {}", email_id);
                    return Ok(());
                } else {
                    eprintln!(
                        "Attempt {} - Failed to delete email with ID: {}. Status: {:?}",
                        attempt,
                        email_id,
                        response.status()
                    );
                }
            }
            Err(err) => {
                eprintln!(
                    "Attempt {} - Error sending request to delete email with ID: {}. Error: {}",
                    attempt,
                    email_id,
                    err
                );
            }
        }

        // // Wait before retrying
        // if attempt < MAX_RETRIES {
        //     tokio::time::sleep(Duration::from_secs(RETRY_DELAY)).await;
        // }
    }
    Err("Failed to mark email as spam after multiple attempts".to_string())
}

#[tauri::command]
pub async fn mark_email_as_spam_in_gmail(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>,
    email_id: String
) -> Result<(), String> {
    // check_and_refresh_token(&app_data)
    //     .await
    //     .map_err(|e| format!("Failed to refresh token: {}", e));

    let client = Client::new();
    // let app_data_arc = app_data.lock().await;
    // let user_data = app_data_arc.user_data.lock().await;
    let mut issued_at_read: Option<DateTime<Utc>> = None;
    let mut refresh_token: Option<String> = None;
    let mut access_token: Option<String> = None;

    {
        let app_data_arc = app_data.read().await; // Acquire read lock
        let user_data = app_data_arc.user_data.read().await;

        issued_at_read = user_data.issued_at.clone();
        refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
        access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
    } // Lock is released here automatically

    let url =
        format!("https://gmail.googleapis.com/gmail/v1/users/me/messages/{}/modify", email_id);

    let body = json!({
        "addLabelIds": ["SPAM"]
    });

    // let access_token = &user_data.refresh_token.as_ref().unwrap().secret();

    const MAX_RETRIES: u8 = 3;
    const RETRY_DELAY: u64 = 2;

    for attempt in 1..=MAX_RETRIES {
        match
            client
                .post(&url)
                .header(AUTHORIZATION, format!("Bearer {}", access_token.clone().unwrap()))
                .header(CONTENT_TYPE, "application/json")
                .json(&body)
                .send().await
        {
            Ok(response) => {
                if response.status().is_success() {
                    println!("Successfully marked email as spam with ID: {}", email_id);
                    return Ok(());
                } else {
                    eprintln!(
                        "Attempt {} - Failed to mark email as spam with ID: {}. Status: {:?}",
                        attempt,
                        email_id,
                        response.status()
                    );
                }
            }
            Err(err) => {
                eprintln!(
                    "Attempt {} - Error sending request to mark email as spam with ID: {}. Error: {}",
                    attempt,
                    email_id,
                    err
                );
            }
        }
    }

    Err("Failed to mark email as spam after multiple attempts".to_string())
}

#[tauri::command]
pub async fn mark_email_as_unread_in_gmail(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>,
    email_id: String
) -> Result<(), String> {
    // check_and_refresh_token(&app_data)
    //     .await
    //     .map_err(|e| format!("Failed to refresh token: {}", e))?;

    let client = Client::new();
    // let app_data_arc = app_data.lock().await;
    // let user_data = app_data_arc.user_data.lock().await;
    let mut issued_at_read: Option<DateTime<Utc>> = None;
    let mut refresh_token: Option<String> = None;
    let mut access_token: Option<String> = None;

    {
        let app_data_arc = app_data.read().await; // Acquire read lock
        let user_data = app_data_arc.user_data.read().await;

        issued_at_read = user_data.issued_at.clone();
        refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
        access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
    } // Lock is released here automatically

    let url =
        format!("https://gmail.googleapis.com/gmail/v1/users/me/messages/{}/modify", email_id);

    let body = json!({
        "addLabelIds": ["UNREAD"]
    });

    // let access_token = &user_data.refresh_token.as_ref().unwrap().secret();

    const MAX_RETRIES: u8 = 3;
    const RETRY_DELAY: u64 = 2;

    for attempt in 1..=MAX_RETRIES {
        match
            client
                .post(&url)
                .header(AUTHORIZATION, format!("Bearer {}", access_token.clone().unwrap()))
                .header(CONTENT_TYPE, "application/json")
                .json(&body)
                .send().await
        {
            Ok(response) => {
                if response.status().is_success() {
                    println!("Successfully marked email as unread with ID: {}", email_id);
                    return Ok(());
                } else {
                    eprintln!(
                        "Attempt {} - Failed to mark email as unread with ID: {}. Status: {:?}",
                        attempt,
                        email_id,
                        response.status()
                    );
                }
            }
            Err(err) => {
                eprintln!(
                    "Attempt {} - Error sending request to mark email as unread with ID: {}. Error: {}",
                    attempt,
                    email_id,
                    err
                );
            }
        }
    }

    Err("Failed to mark email as unread after multiple attempts".to_string())
}

#[tauri::command]
pub async fn mark_email_as_read_in_gmail(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>,
    email_id: String
) -> Result<(), String> {
    // check_and_refresh_token(&app_data)
    //     .await
    //     .map_err(|e| format!("Failed to refresh token: {}", e))?;

    let client = Client::new();
    // let app_data_arc = app_data.lock().await;
    // let user_data = app_data_arc.user_data.lock().await;
    let mut issued_at_read: Option<DateTime<Utc>> = None;
    let mut refresh_token: Option<String> = None;
    let mut access_token: Option<String> = None;

    {
        let app_data_arc = app_data.read().await; // Acquire read lock
        let user_data = app_data_arc.user_data.read().await;

        issued_at_read = user_data.issued_at.clone();
        refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
        access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
    } // Lock is released here automatically

    let url =
        format!("https://gmail.googleapis.com/gmail/v1/users/me/messages/{}/modify", email_id);

    let body = json!({
        "removeLabelIds": ["UNREAD"]
    });

    // let access_token = &user_data.refresh_token.as_ref().unwrap().secret();

    const MAX_RETRIES: u8 = 3;
    const RETRY_DELAY: u64 = 2;

    for attempt in 1..=MAX_RETRIES {
        match
            client
                .post(&url)
                .header(AUTHORIZATION, format!("Bearer {}", access_token.clone().unwrap()))
                .header(CONTENT_TYPE, "application/json")
                .json(&body)
                .send().await
        {
            Ok(response) => {
                if response.status().is_success() {
                    println!("Successfully marked email as read with ID: {}", email_id);
                    return Ok(());
                } else {
                    eprintln!(
                        "Attempt {} - Failed to mark email as read with ID: {}. Status: {:?}",
                        attempt,
                        email_id,
                        response.status()
                    );
                }
            }
            Err(err) => {
                eprintln!(
                    "Attempt {} - Error sending request to mark email as read with ID: {}. Error: {}",
                    attempt,
                    email_id,
                    err
                );
            }
        }
    }

    Err("Failed to mark email as read after multiple attempts".to_string())
}

// #[tauri::command]
// pub async fn send_email(
//     app_data: tauri::State<'_, Arc<RwLock<AppData>>>,
//     recipient: String,
//     cc: Option<String>,
//     bcc: Option<String>,
//     subject: String,
//     body: String,
//     attachments: Vec<PathBuf>, // Attachment file paths
// ) -> Result<(), String> {
//     // let mut logged_in = app_data.logged_in.lock().await;
//     //   let user_data_arc = app_data.lock().await.user_data.clone(); // Lock before accessing user_data
//     // check_and_refresh_token(&app_data)
//     //     .await
//     //     .map_err(|e| format!("Failed to refresh token: {}", e))?;

//     // let app_data_guard = app_data.lock().unwrap();  // Lock the Mutex
//     // Step 2: Call `check_and_refresh_token_unread` with `user_data_arc`
//     let mut refresh_token: Option<String> = None;
//     let mut access_token: Option<String> = None;

//     {
//         let app_data_arc = app_data.read().await; // Acquire read lock
//         let user_data = app_data_arc.user_data.read().await;

//         refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
//         access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
//     } // Lock is released here automatically

//     // Step 2: Access the updated user_data and get the access and refresh tokens
//     // let (access_token, refresh_token);
//     // {
//     //     let user_data_lock = user_data_arc.lock().await;

//     //     // Retrieve access token
//     //     access_token = match &user_data_lock.access_token {
//     //         Some(token) => token.secret().clone(),
//     //         None => return Err("Access token is missing".to_string()),
//     //     };

//     //     // Retrieve refresh token
//     //     refresh_token = match &user_data_lock.refresh_token {
//     //         Some(token) => token.secret().clone(),
//     //         None => return Err("Refresh token is missing".to_string()), // Adjust error message as needed
//     //     };
//     // } // The lock on `user_data_arc` is released here

//     let client = Client::new();

//     // Create the email message in RFC 2822 format
//     let mut email = format!("To: {}\r\nSubject: {}\r\n\r\n{}", recipient, subject, body);

//     // Add CC and BCC if provided
//     if let Some(cc) = cc {
//         email.push_str(&format!("\r\nCc: {}", cc));
//     }
//     if let Some(bcc) = bcc {
//         email.push_str(&format!("\r\nBcc: {}", bcc));
//     }

//     // If there are attachments, append them to the email
//     if !attachments.is_empty() {
//         email = add_attachments_to_email(email, &attachments)?;
//     }

//     // Print the final email format before encoding
//     println!("Final email format:\n{}", email);

//     // Encode the email message in base64 for the Gmail API
//     let encoded_email = base64::encode_config(email, base64::URL_SAFE);

//     // Print the encoded email to verify it’s properly formatted
//     println!("Encoded email (base64): {}", encoded_email);

//     // The Gmail API URL to send the email
//     let url = "https://gmail.googleapis.com/gmail/v1/users/me/messages/send";
//     println!("Sending email to URL: {}", url);

//     let body = json!({
//         "raw": encoded_email
//     });

//     // Send the email request to Gmail API
//     let response = client
//         .post(url)
//         .bearer_auth(&access_token.clone().unwrap())
//         .json(&body)
//         .send()
//         .await;

//     match response {
//         Ok(res) if res.status().is_success() => {
//             println!("Email sent successfully.");
//             Ok(())
//         }
//         Ok(res) => {
//             // Print the full response body if available
//             // Capture status before consuming `res` with `.text()`
//             let status = res.status();
//             let error_body = res
//                 .text()
//                 .await
//                 .unwrap_or_else(|_| "Failed to read error body".to_string());
//             eprintln!(
//                 "Failed to send email. Status: {:?}, Error: {}",
//                 status, error_body
//             );
//             Err(format!(
//                 "Failed to send email. Status: {:?}, Error: {}",
//                 status, error_body
//             ))
//         }
//         Err(err) => {
//             eprintln!("Error sending email: {:?}", err);
//             Err(format!("Error sending email: {:?}", err))
//         }
//     }
// }
// // Helper function to attach files to the email
// fn add_attachments_to_email(mut email: String, attachments: &[PathBuf]) -> Result<String, String> {
//     for attachment in attachments {
//         let file_content = std::fs::read(attachment)
//             .map_err(|_| format!("Failed to read attachment: {:?}", attachment))?;

//         // Base64 encode the file content
//         let encoded_content = base64::encode(file_content);

//         // Append the attachment to the email body
//         email.push_str(&format!(
//             "\r\n--boundary\r\nContent-Type: application/octet-stream\r\nContent-Transfer-Encoding: base64\r\nContent-Disposition: attachment; filename=\"{}\"\r\n\r\n{}\r\n--boundary--\r\n",
//             attachment.file_name().unwrap().to_string_lossy(),
//             encoded_content
//         ));
//     }

//     Ok(email)
// }

// #[tauri::command]
// pub async fn send_email(
//     app_data: tauri::State<'_, Arc<RwLock<AppData>>>,
//     recipient: String,
//     cc: Option<String>,
//     bcc: Option<String>,
//     subject: String,
//     body: String,
//     attachments: Vec<PathBuf>
// ) -> Result<(), String> {
//     // ... [token handling code remains the same] ...

//     let boundary = format!("_=====BOUNDARY_{}=====_", uuid::Uuid::new_v4()); // Generate unique boundary
//     let mut refresh_token: Option<String> = None;
//     let mut access_token: Option<String> = None;

//     {
//         let app_data_arc = app_data.read().await; // Acquire read lock
//         let user_data = app_data_arc.user_data.read().await;

//         refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
//         access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
//     } // Lock is released here automatically
//     // Build email headers
//     let mut headers = format!(
//         "To: {}\r\n\
//          Subject: {}\r\n\
//          MIME-Version: 1.0\r\n\
//          Content-Type: multipart/mixed; boundary=\"{}\"\r\n",
//         recipient,
//         subject,
//         boundary
//     );

//     // Add CC/BCC headers
//     if let Some(cc) = &cc {
//         headers.push_str(&format!("Cc: {}\r\n", cc));
//     }
//     if let Some(bcc) = &bcc {
//         headers.push_str(&format!("Bcc: {}\r\n", bcc));
//     }
//     headers.push_str("\r\n");

//     // Start building the email body
//     let mut email = headers;

//     // Add HTML body part
//     email.push_str(
//         &format!(
//             "--{}\r\n\
//          Content-Type: text/html; charset=UTF-8\r\n\
//          Content-Transfer-Encoding: quoted-printable\r\n\r\n\
//          {}\r\n",
//             boundary,
//             body
//         )
//     );

//     // Add attachments
//     email = add_attachments_to_email(email, &attachments, &boundary)?;

//     // Close the multipart boundary
//     email.push_str(&format!("--{}--\r\n", boundary));

//     // ... [rest of the code remains the same] ...

//     // Print the final email format before encoding
//     println!("Final email format:\n{}", email);

//     // Encode the email message in base64 for the Gmail API
//     let encoded_email = base64::encode_config(email, base64::URL_SAFE);

//     // Print the encoded email to verify it’s properly formatted
//     // println!("Encoded email (base64): {}", encoded_email);

//     // The Gmail API URL to send the email
//     let url = "https://gmail.googleapis.com/gmail/v1/users/me/messages/send";
//     // println!("Sending email to URL: {}", url);

//     let body = json!({
//         "raw": encoded_email
//     });

//     let client = Client::new();
//     // Send the email request to Gmail API
//     let response = client
//         .post(url)
//         .bearer_auth(&access_token.clone().unwrap())
//         .json(&body)
//         .send().await;

//     match response {
//         Ok(res) if res.status().is_success() => {
//             println!("Email sent successfully.");
//             Ok(())
//         }
//         Ok(res) => {
//             // Print the full response body if available
//             // Capture status before consuming `res` with `.text()`
//             let status = res.status();
//             let error_body = res
//                 .text().await
//                 .unwrap_or_else(|_| "Failed to read error body".to_string());
//             eprintln!("Failed to send email. Status: {:?}, Error: {}", status, error_body);
//             Err(format!("Failed to send email. Status: {:?}, Error: {}", status, error_body))
//         }
//         Err(err) => {
//             eprintln!("Error sending email: {:?}", err);
//             Err(format!("Error sending email: {:?}", err))
//         }
//     }
// }

#[tauri::command]
pub async fn send_email(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>,
    recipient: String,
    cc: Option<String>,
    bcc: Option<String>,
    subject: String,
    body: String,
    attachments: Vec<PathBuf>,
    in_reply_to: Option<String>,
    references: Option<String>,
    thread_id: Option<String>
) -> Result<(), String> {
    println!("Sending email to: {} ", recipient);
    println!("In Reply: {:?} ", in_reply_to);
    let boundary = format!("_=====BOUNDARY_{}=====_", uuid::Uuid::new_v4());
    let mut refresh_token: Option<String> = None;
    let mut access_token: Option<String> = None;

    {
        let app_data_arc = app_data.read().await;
        let user_data = app_data_arc.user_data.read().await;

        refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
        access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
    }

    // If in_reply_to is provided, fetch the original message to get proper Message-ID and thread_id
    let (proper_message_id, proper_thread_id) = if let Some(original_message_id) = &in_reply_to {
        // First, try to get thread_id from database
        let db_thread_id = crate::services::emails_service
            ::get_email_by_id(original_message_id)
            .ok()
            .and_then(|email| email.thread_id);

        match fetch_message(&access_token.clone().unwrap(), original_message_id).await {
            Ok(original_message) => {
                println!(
                    "🔍 DEBUG: Fetched original message headers: {:?}",
                    original_message.headers
                );

                let message_id = original_message.headers
                    .get("Message-ID")
                    .map(|s| s.to_string())
                    .unwrap_or_default();

                println!("🔍 DEBUG: Extracted Message-ID: '{}'", message_id);
                println!("🔍 DEBUG: Database thread_id: {:?}", db_thread_id);
                println!("🔍 DEBUG: Provided thread_id: {:?}", thread_id);

                // Use provided thread_id, or fall back to database thread_id
                let thread_id_to_use = thread_id.clone().or(db_thread_id);

                println!("🔍 DEBUG: Final thread_id to use: {:?}", thread_id_to_use);

                (Some(message_id), thread_id_to_use)
            }
            Err(e) => {
                eprintln!("Failed to fetch original message: {}", e);
                // Still try to use database thread_id even if fetching message fails
                let thread_id_to_use = thread_id.clone().or(db_thread_id);
                (in_reply_to.clone(), thread_id_to_use)
            }
        }
    } else {
        (None, thread_id)
    };

    // Build headers
    let mut headers = format!(
        "To: {}\r\n\
         Subject: {}\r\n\
         MIME-Version: 1.0\r\n\
         Content-Type: multipart/mixed; boundary=\"{}\"\r\n",
        recipient,
        subject,
        boundary
    );

    // Add CC/BCC
    if let Some(cc) = &cc {
        headers.push_str(&format!("Cc: {}\r\n", cc));
    }
    if let Some(bcc) = &bcc {
        headers.push_str(&format!("Bcc: {}\r\n", bcc));
    }

    // Add reply headers with proper Message-ID format
    if let Some(message_id) = &proper_message_id {
        if !message_id.is_empty() {
            headers.push_str(&format!("In-Reply-To: {}\r\n", message_id));
            // For References, use the same Message-ID (or you could build a chain if needed)
            headers.push_str(&format!("References: {}\r\n", message_id));
            println!("🔍 DEBUG: Added reply headers with Message-ID: '{}'", message_id);
        } else {
            println!("⚠️ DEBUG: Message-ID is empty, not adding reply headers");
        }
    } else {
        println!("⚠️ DEBUG: No Message-ID found, not adding reply headers");
    }

    headers.push_str("\r\n");

    // Email body
    let mut email = headers;

    email.push_str(
        &format!(
            "--{}\r\n\
         Content-Type: text/html; charset=UTF-8\r\n\
         Content-Transfer-Encoding: quoted-printable\r\n\r\n\
         {}\r\n",
            boundary,
            body
        )
    );

    // Add attachments
    email = add_attachments_to_email(email, &attachments, &boundary)?;

    // End boundary
    email.push_str(&format!("--{}--\r\n", boundary));

    // Print raw email
    println!("Final email format:\n{}", email);

    // Encode base64 (your original way)
    let encoded_email = base64::encode_config(email, base64::URL_SAFE);

    // Prepare request body with proper thread_id
    let body = if let Some(thread_id) = &proper_thread_id {
        println!("🔍 DEBUG: Including threadId in request: '{}'", thread_id);
        serde_json::json!({
            "raw": encoded_email,
            "threadId": thread_id
        })
    } else {
        println!("⚠️ DEBUG: No threadId provided, sending as new email");
        serde_json::json!({
            "raw": encoded_email
        })
    };

    println!(
        "🔍 DEBUG: Final request body: {}",
        serde_json::to_string_pretty(&body).unwrap_or_default()
    );

    let client = reqwest::Client::new();
    let url = "https://gmail.googleapis.com/gmail/v1/users/me/messages/send";

    let response = client
        .post(url)
        .bearer_auth(&access_token.clone().unwrap())
        .json(&body)
        .send().await;

    match response {
        Ok(res) if res.status().is_success() => {
            println!("Email sent successfully.");
            Ok(())
        }
        Ok(res) => {
            let status = res.status();
            let error_body = res
                .text().await
                .unwrap_or_else(|_| "Failed to read error body".to_string());
            eprintln!("Failed to send email. Status: {:?}, Error: {}", status, error_body);
            Err(format!("Failed to send email. Status: {:?}, Error: {}", status, error_body))
        }
        Err(err) => {
            eprintln!("Error sending email: {:?}", err);
            Err(format!("Error sending email: {:?}", err))
        }
    }
}

fn add_attachments_to_email(
    mut email: String,
    attachments: &[PathBuf],
    boundary: &str
) -> Result<String, String> {
    for attachment in attachments {
        let content = std::fs
            ::read(attachment)
            .map_err(|e| format!("Failed to read attachment: {}", e))?;

        let filename = attachment
            .file_name()
            .ok_or_else(|| format!("Invalid filename: {:?}", attachment))?
            .to_string_lossy();

        let mime_type = mime_guess
            ::from_path(attachment)
            .first()
            .unwrap_or(mime::APPLICATION_OCTET_STREAM);

        let encoded_content = base64::encode(&content);

        email.push_str(
            &format!(
                "--{}\r\n\
             Content-Type: {}\r\n\
             Content-Disposition: attachment; filename=\"{}\"\r\n\
             Content-Transfer-Encoding: base64\r\n\r\n\
             {}\r\n",
                boundary,
                mime_type,
                filename,
                encoded_content
            )
        );
    }

    Ok(email)
}

#[tauri::command]
pub async fn reply_to_email(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>,
    original_message_id: String, // The ID of the message you're replying to
    recipient: String,
    cc: Option<String>,
    bcc: Option<String>,
    subject: String,
    body: String,
    attachments: Vec<PathBuf>
) -> Result<(), String> {
    // Get tokens (same as your original code)
    let mut refresh_token: Option<String> = None;
    let mut access_token: Option<String> = None;
    {
        let app_data_arc = app_data.read().await;
        let user_data = app_data_arc.user_data.read().await;
        refresh_token = user_data.refresh_token.as_ref().map(|t| t.secret().clone());
        access_token = user_data.access_token.as_ref().map(|t| t.secret().clone());
    }

    // First, fetch the original message to get its headers
    let original_message = fetch_message(
        &access_token.clone().unwrap(),
        &original_message_id
    ).await?;

    // Extract important headers from the original message
    let original_subject = original_message.headers
        .get("Subject")
        .map(|s| s.to_string())
        .unwrap_or_else(|| subject.clone());

    let original_message_id_header = original_message.headers
        .get("Message-ID")
        .map(|s| s.to_string())
        .unwrap_or_default();

    // Generate boundary
    let boundary = format!("_=====BOUNDARY_{}=====_", uuid::Uuid::new_v4());

    // Build email headers with reply metadata
    let mut headers = format!(
        "To: {}\r\n\
         Subject: Re: {}\r\n\
         References: {}\r\n\
         In-Reply-To: {}\r\n\
         MIME-Version: 1.0\r\n\
         Content-Type: multipart/mixed; boundary=\"{}\"\r\n",
        recipient,
        original_subject.trim_start_matches("Re: ").trim_start_matches("Fwd: "),
        original_message_id_header,
        original_message_id_header,
        boundary
    );

    // Add CC/BCC headers (same as original)
    if let Some(cc) = &cc {
        headers.push_str(&format!("Cc: {}\r\n", cc));
    }
    if let Some(bcc) = &bcc {
        headers.push_str(&format!("Bcc: {}\r\n", bcc));
    }
    headers.push_str("\r\n");

    // Build email body (same as original)
    let mut email = headers;
    email.push_str(
        &format!(
            "--{}\r\n\
         Content-Type: text/html; charset=UTF-8\r\n\
         Content-Transfer-Encoding: quoted-printable\r\n\r\n\
         {}\r\n",
            boundary,
            body
        )
    );

    // Add attachments (same as original)
    email = add_attachments_to_email(email, &attachments, &boundary)?;
    email.push_str(&format!("--{}--\r\n", boundary));

    // Send the email (same as original)
    let encoded_email = base64::encode_config(email, base64::URL_SAFE);
    let url = "https://gmail.googleapis.com/gmail/v1/users/me/messages/send";

    let client = Client::new();
    let response = client
        .post(url)
        .bearer_auth(&access_token.clone().unwrap())
        .json(&json!({ "raw": encoded_email }))
        .send().await;

    match response {
        Ok(res) if res.status().is_success() => Ok(()),
        Ok(res) => {
            let status = res.status();
            let error_body = res.text().await.unwrap_or_default();
            Err(format!("Failed to send reply. Status: {}, Error: {}", status, error_body))
        }
        Err(err) => Err(format!("Error sending reply: {}", err)),
    }
}

async fn fetch_message(access_token: &str, message_id: &str) -> Result<MessageData, String> {
    let url = format!("https://gmail.googleapis.com/gmail/v1/users/me/messages/{}", message_id);
    let client = Client::new();

    let response = client
        .get(&url)
        .bearer_auth(access_token)
        .send().await
        .map_err(|e| format!("Failed to fetch message: {}", e))?;

    if !response.status().is_success() {
        let error_body = response.text().await.unwrap_or_default();
        return Err(format!("Failed to fetch message: {}", error_body));
    }

    let message: serde_json::Value = response
        .json().await
        .map_err(|e| format!("Failed to parse message: {}", e))?;

    // Extract headers from the message
    let payload = message["payload"].as_object().ok_or("Invalid message payload")?;
    let headers = payload["headers"].as_array().ok_or("Invalid headers")?;

    let mut message_data = MessageData {
        headers: std::collections::HashMap::new(),
    };

    for header in headers {
        if let (Some(name), Some(value)) = (header["name"].as_str(), header["value"].as_str()) {
            message_data.headers.insert(name.to_string(), value.to_string());
        }
    }

    Ok(message_data)
}

#[derive(Debug)]
struct MessageData {
    headers: std::collections::HashMap<String, String>,
}
