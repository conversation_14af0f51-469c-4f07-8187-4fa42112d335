<template>
  <QuillEditor v-model:content="content" :options="options" content-type="html">
    <!-- <template #toolbar>
      <div id="my-toolbar">

        <ToolbarGroup>
          <QLButton format="header" :value="1" />
          <QLButton format="header" :value="2" />
        </ToolbarGroup>
        <ToolbarGroup>
          <QLButton format="bold" />
          <QLButton format="italic" />
          <QLButton format="underline" />
          <QLButton format="strike" />
        </ToolbarGroup>

        <button class="ql-blockquote"></button>
        <button id="custom-button"></button>
      </div>
    </template> -->
  </QuillEditor>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Delta, QuillEditor } from "@vueup/vue-quill";
import "./vue-quill.snow.css";
// import "@vueup/vue-quill/dist/vue-quill.bubble.css";

interface Props {
  modelValue?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
});

const emit = defineEmits<{
  "update:modelValue": [value: string];
}>();

// Create a computed property for two-way binding
const content = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const customToolbar = [
  ["bold", "italic", "underline", "strike"],
  ["blockquote", "code-block"],
  [{ header: 1 }, { header: 2 }],
  [{ list: "ordered" }, { list: "bullet" }],
  [{ script: "sub" }, { script: "super" }],
  [{ indent: "-1" }, { indent: "+1" }],
  [{ direction: "rtl" }],
  [{ size: ["small", false, "large", "huge"] }],
  [{ header: [1, 2, 3, 4, 5, 6, false] }],
  [{ color: [] }, { background: [] }],
  [{ font: [] }],
  [{ align: [] }],
  ["link", "video", "image"],
  ["clean"], // remove formatting button
];

const options = {
  modules: {
    toolbar: customToolbar,
  },
};
</script>

<style scoped></style>
