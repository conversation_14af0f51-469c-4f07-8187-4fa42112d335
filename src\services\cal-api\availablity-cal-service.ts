import axios from "axios";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CalUser } from "../../models/user";
import CalApi from "./cal-api-service";
import { CalAvailability } from "../../models/availability-model";

interface CalUsersRes {
  users: CalUser[]
}

interface CalUserRes {
  user: CalUser
}

async function updateAvailability(avId: number, av: CalAvailability): Promise<CalAvailability | null> {
  try {
    delete av.id;
    delete av.scheduleId;
    const response = await CalApi.patch<CalAvailability>(`api/availabilities/${avId}`, av);
    return response.data;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}


async function removeAvailability(avId: number): Promise<CalAvailability | null> {
  try {
    const response = await CalApi.delete<CalAvailability>(`api/availabilities/${avId}`);
    return response.data;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}

async function createAvailability(av: CalAvailability): Promise<CalAvailability | null> {
  try {
    delete av.id;

    const response = await CalApi.post<CalAvailability>(`api/availabilities`, av);
    return response.data;
  } catch (error) {
    console.error("ERR => ", error);
    return null
  }
}


async function getAvailabilities(scheduleId: number): Promise<CalAvailability[] | null> {
  try {
    //  console.log("FETCHING AVAILABILITIES FOR SCHEDULE ID =>", scheduleId);

    const response = await CalApi.get<{ availabilities: CalAvailability[] }>(
      `api/schedules/${scheduleId}`, { params: { scheduleId } }
    );
    console.log("🚀 ~ getAvailabilities ~ response.data.availabilities:", response.data.availabilities)
    return response.data.availabilities;
  } catch (error) {
    console.error("ERROR FETCHING AVAILABILITIES =>", error);
    return null;
  }
}

export const calAvailabilityService = {
  updateAvailability,
  removeAvailability,
  createAvailability,
  getAvailabilities
}
