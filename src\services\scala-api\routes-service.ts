import { toast } from "vue-sonner";
import { useCurrentUserStore } from "../../stores/currentUser";
import ScalaApi from "./scala-api";
import { EmailSummaries } from "./dto";

function getScalaUserName() {
  const userStore = useCurrentUserStore();
  return userStore.currentUser?.email.split("@")[0] ?? null;
}


async function getEmailSummaries(emailId: string): Promise<EmailSummaries | undefined> {
  try {
    const userName = getScalaUserName();
    if (userName === null) throw new Error("No user name found");
    const result: any = await ScalaApi.get<EmailSummaries>(`/email-summaries/${emailId}`, { params: { userId: userName } });
    console.log("🚀 ~ getEmailSummaries ~ result.data:", result.data)
    return result.data;
  } catch (error: any) {
    console.error("❌ Failed to get email summaries:", error);
    // toast.error("Failed to get email summaries");
  }
}


export const ScalaRoutesService = {
  getEmailSummaries
}
