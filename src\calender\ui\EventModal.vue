<template>
  <div class="size-full flex justify-between items-center pb-2">
    <!-- Sidebar -->
    <div class="w-1/4 h-full py-6 px-4 bg-primary border-r border-primary-400">
      <!-- Header -->
      <div class="flex justify-between items-center mb-6">
        <button
          @click="() => emit('close')"
          class="p-2 text-secondary-500 hover:text-secondary-700 hover:bg-primary-200 rounded-lg transition-all duration-200"
        >
          <ChevronLeftIcon class="w-5 h-5" />
        </button>
        <h2 class="text-lg font-semibold text-base-500">Events</h2>
        <button
          @click="
            selEvent = null;
            editableSch = null;
          "
          class="p-2 text-highlight-600 hover:text-highlight-700 hover:bg-highlight-100 rounded-lg transition-all duration-200"
          title="Create new event"
        >
          <PlusIcon class="w-4 h-4" />
        </button>
      </div>

      <!-- Event List -->
      <div class="space-y-2">
        <div
          class="group relative p-3 rounded-lg border transition-all duration-200 cursor-pointer"
          v-for="event in events"
          :key="event.id"
          :class="
            editableSch && editableSch.id == event.id
              ? 'bg-secondary-100 border-secondary-300 shadow-sm'
              : 'bg-primary-200 border-primary-400 hover:bg-primary-300 hover:border-primary-500'
          "
          @click="() => selectSchedule(event)"
        >
          <div class="flex justify-between items-start">
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 mb-1">
                <h3 class="text-sm font-medium text-base-500 truncate">{{ event.title }}</h3>
                <span
                  v-if="event.hidden"
                  class="text-xs bg-accent-200 text-accent-700 px-2 py-0.5 rounded-full font-medium"
                >
                  Hidden
                </span>
              </div>
              <p class="text-xs text-secondary-600">{{ event.length }} min • {{ event.slug }}</p>
            </div>

            <!-- Action buttons -->
            <div class="group-hover:flex hidden items-center gap-1.5 ml-2">
              <button
                v-if="event.id !== undefined"
                @click.stop="async () => await openEventLink()"
                class="p-1.5 bg-primary-300 text-secondary-600 hover:bg-primary-400 rounded-lg transition-colors duration-200"
                title="Open event link"
              >
                <PaperClipIcon class="w-3 h-3" />
              </button>
              <button
                @click.stop="async () => await copyLink()"
                class="p-1.5 bg-primary-300 text-secondary-600 hover:bg-primary-400 rounded-lg transition-colors duration-200"
                title="Copy link"
              >
                <CheckIcon v-if="selLink == copiedLink" class="w-3 h-3" />
                <LinkIcon v-else class="w-3 h-3" />
              </button>
              <button
                @click.stop="() => {}"
                class="p-1.5 bg-accent-200 text-accent-700 hover:bg-accent-300 rounded-lg transition-colors duration-200"
                title="Delete event"
              >
                <TrashIcon class="w-3 h-3" />
              </button>
            </div>
          </div>
        </div>

        <!-- Empty state -->
        <div v-if="!events || events.length === 0" class="text-center py-8">
          <div class="text-secondary-400 mb-2">
            <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1"
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              ></path>
            </svg>
          </div>
          <p class="text-sm text-secondary-600 mb-3">No events yet</p>
          <button
            @click="
              selEvent = null;
              editableSch = null;
            "
            class="text-sm text-highlight-600 hover:text-highlight-700 font-medium"
          >
            Create your first event
          </button>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div
      :key="selEvent?.id"
      class="w-3/4 h-full py-2 px-4 flex flex-col gap-4 overflow-y-auto custom-scrollbar bg-primary-400/20"
    >
      <EventTypeForm :data="editableSch?.id ? editableSch : defaultEventType" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";
import { useCurrentUserStore } from "../../stores/currentUser";
import {
  PaperClipIcon,
  CheckIcon,
  ChevronLeftIcon,
  EyeIcon,
  GlobeAltIcon,
  LinkIcon,
  PencilIcon,
  PlusIcon,
  TrashIcon,
} from "@heroicons/vue/24/outline";

import { EventType } from "../../models/event-type-model";
import { calEventTypeService } from "../../services/cal-api/event-type-cal-service";
import { open } from "@tauri-apps/plugin-shell";
import { writeText } from "@tauri-apps/plugin-clipboard-manager";
import EventTypeForm from "../forms/event-type/EventTypeForm.vue";
import { defaultEventType } from "../../models/event-type-model";

const emit = defineEmits(["close"]);

const copiedLink = ref<string | null>(null);

const events = ref<EventType[]>([]);
const selEvent = ref<EventType | null>(null);
const session = useCurrentUserStore();
const editableSch = ref<EventType | null>(null);
const selLink = computed<string | null>(() => {
  return selEvent.value?.slug && session.calInfo?.username
    ? `${import.meta.env.VITE_CAL_API_HOST}/events/${session.calInfo?.username}/${selEvent.value.slug}`
    : null;
});

async function getEventTypes() {
  if (session.calInfo?.id) events.value = (await calEventTypeService.getUserEventType(session.calInfo?.id)) ?? [];
}

function selectSchedule(s: EventType) {
  selEvent.value = s;
  editableSch.value = { ...s };
}

async function copyLink() {
  console.log("🚀 ~ copyLink ~ selLink.value:", selLink.value);
  if (!selLink.value) return;
  await writeText(selLink.value)
    .then(() => {
      copiedLink.value = selLink.value;
      setTimeout(() => {
        copiedLink.value = null;
      }, 2000);
    })
    .catch((e: any) => console.error(e));
}

async function openEventLink() {
  if (selLink.value) {
    await open(selLink.value);
  } else {
    console.error("Username or Event ID is missing");
  }
}

watch(
  selEvent,
  (value) => {
    console.log("Selected event", value);
  },
  { deep: true }
);

onMounted(async () => {
  await getEventTypes();
});
</script>

<style scoped></style>
