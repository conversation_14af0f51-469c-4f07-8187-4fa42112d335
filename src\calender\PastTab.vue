<template>
  <div class="size-full flex">
    <div class="flex flex-col gap-2 flex-1 p-2 overflow-y-auto custom-scrollbar">
      <div v-for="(monthMeetings, month) in groupedMeetings" :key="month" class="mb-2">
        <button
          @click="toggleMonth(month)"
          class="w-full text-left font-semibold text-secondary-800 py-1 px-2 bg-secondary-100 rounded hover:bg-secondary-200"
        >
          📅 {{ month }} <span>{{ collapsedMonths[month] ? '▶' : '▼' }}</span>
        </button>
        <div v-if="!collapsedMonths[month]" class="flex flex-col gap-2 mt-1">
          <MeetingCard
            v-for="meeting in monthMeetings"
            :key="meeting.id"
            :meeting="meeting"
          />
        </div>
      </div>
    </div>
    <MeetingDrawer :meeting="meetingsStore.selectedMeeting ?? undefined" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { useMeetingsStore } from "../stores/meetingsStore";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import MeetingCard from "./ui/MeetingCard.vue";
import MeetingDrawer from "./ui/MeetingDrawer.vue";

const meetingsStore = useMeetingsStore();
const { meetings } = storeToRefs(meetingsStore);

const collapsedMonths = ref<Record<string, boolean>>({});

// Group meetings by month (e.g., "July 2025")
const groupedMeetings = computed(() => {
  const groups: Record<string, typeof meetings.value> = {};
  meetings.value.forEach((meeting) => {
    const month = dayjs(meeting.createdAt).format("MMMM YYYY");
    if (!groups[month]) groups[month] = [];
    groups[month].push(meeting);
  });
  return groups;
});

function toggleMonth(month: string) {
  collapsedMonths.value[month] = !collapsedMonths.value[month];
}
</script>

<style scoped></style>