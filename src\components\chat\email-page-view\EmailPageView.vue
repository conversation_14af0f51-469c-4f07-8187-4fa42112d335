<template>
  <div class="fixed inset-0 bg-black/50 flex items-center justify-center py-6 px-14">
    <!-- Navigation Buttons -->
    <button
      @click="emitEvent('goPrevious')"
      :disabled="prevDisabled"
      class="absolute left-4 top-1/2 z-50 bg-primary-200/80 backdrop-blur-sm -translate-y-1/2 p-2 rounded-lg hover:bg-primary-300 transition-all duration-200 shadow-lg border border-primary-300 disabled:opacity-50 disabled:cursor-not-allowed"
    >
      <ChevronLeftIcon class="w-6 h-6 text-secondary-700" />
    </button>

    <button
      @click="emitEvent('goNext')"
      :disabled="nextDisabled"
      class="absolute right-4 top-1/2 z-50 bg-primary-200/80 backdrop-blur-sm -translate-y-1/2 p-2 rounded-lg hover:bg-primary-300 transition-all duration-200 shadow-lg border border-primary-300 disabled:opacity-50 disabled:cursor-not-allowed"
    >
      <ChevronRightIcon class="w-6 h-6 text-secondary-700" />
    </button>

    <!-- Main Email Container -->
    <div class="bg-primary w-full h-full rounded-xl shadow-2xl overflow-hidden flex flex-col border border-primary-300">
      <!-- Email Header -->
      <EmailHeader
        :subject="email?.subject"
        :cc="email?.cc"
        :bcc="email?.bcc"
        :can-snooze="canSnoozeEmail"
        @archive="archiveEmail"
        @mark-as-unread="markAsUnread"
        @snooze="openSnoozeModal"
        @delete="deleteEmail"
        @close="emitEvent('close')"
      />

      <!-- Email Content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Messages Container -->
        <div :key="fetchTry" @scroll="onScroll" class="flex-1 overflow-y-auto px-6 py-4 space-y-6" id="html-container">
          <!-- Email Messages -->
          <div v-for="(msg, index) in emailMessages" :key="msg.id || index">
            <EmailMessage
              :message="msg"
              :attachments="msg.attachments"
              :thread="emailMessages"
              @reply="replyEmail"
              @reply-all="replyAllEmail"
              @forward="forwardEmail"
              @star="starMessage"
              @archive="archiveMessage"
              @delete="deleteMessage"
              :last="index === emailMessages.length - 1"
            />
          </div>

          <!-- Draft Section -->
          <EmailDraft
            v-if="draft"
            :draft="draft"
            @send-draft="sendDraft"
            @edit-draft="editDraft"
            @delete-draft="refuseDraft"
          />
        </div>

        <!-- Scroll to Bottom Button -->
        <button
          v-if="showGoToBottom"
          @click="scrollToBottom"
          class="fixed bottom-24 right-8 w-12 h-12 bg-secondary-500 hover:bg-secondary-600 text-primary-100 rounded-full shadow-lg transition-all duration-200 flex items-center justify-center z-40"
        >
          <ChevronDownIcon class="w-5 h-5" />
        </button>

        <!-- Auto Reply Suggestion -->
        <div v-if="autoReply" class="mx-6 mb-4 p-4 bg-highlight-100 border border-highlight-300 rounded-lg">
          <h3 class="text-lg font-semibold mb-2 text-highlight-800">AI Reply Suggestion</h3>
          <p class="text-highlight-700">{{ autoReply }}</p>
          <div class="mt-3 flex gap-2">
            <button
              @click="useAutoReply"
              class="px-4 py-2 bg-highlight-500 text-primary-100 rounded-lg hover:bg-highlight-600 transition-colors"
            >
              Use This Reply
            </button>
            <button
              @click="autoReply = ''"
              class="px-4 py-2 bg-primary-200 text-secondary-700 rounded-lg hover:bg-primary-300 transition-colors"
            >
              Dismiss
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Snooze Modal -->
    <SnoozModal
      v-model="showSnoozeModal"
      :thread-id="email?.thread_id || props.threadId"
      :email-category="email?.storage_location"
      @snoozed="handleEmailSnoozed"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUpdated, computed, watch } from "vue";
import { useCurrentUserStore } from "../../../stores/currentUser";
import { invoke } from "@tauri-apps/api/core";
import { Email } from "../../../types";
import { GmailMessage } from "../../../types";
import { ChevronLeftIcon, ChevronRightIcon, ChevronDownIcon } from "@heroicons/vue/24/outline";
import { Draft } from "../../../models/task-model";
import emailCommands from "../../../commands/emails";
import SnoozModal from "../../modals/SnoozModal.vue";
import { SNOOZABLE_CATEGORIES } from "../../../commands/snooze";
import { emit } from "@tauri-apps/api/event";
import { useCurrentEmailCategoryStore } from "../../../stores/currentSession";

// Import new components
import EmailHeader from "./EmailHeader.vue";
import EmailMessage from "./EmailMessage.vue";
import EmailDraft from "./EmailDraft.vue";
import { EmailMessage as EmailMsg } from "./email.types";
import * as cheerio from "cheerio";

const showGoToBottom = ref(true);
const showSnoozeModal = ref(false);

const currentUserStore = useCurrentUserStore();

const emitEvent = defineEmits(["goNext", "goPrevious", "close"]);

const props = defineProps({
  emails: Object,
  threadId: String,
  access_token: String,
  nextDisabled: Boolean,
  prevDisabled: Boolean,
});

const drafts = ref<Draft[]>([]);

const threadEmails = ref<Email[]>([]);
const emails = computed<Email[] | null>(() => {
  if (threadEmails.value.length > 0) {
    return threadEmails.value;
  }
  return props.emails
    ? Array.from(new Map((props.emails as Email[]).map((item: Email) => [item.id, item])).values())
    : null;
});
const email = ref<Email | null>(emails.value ? emails.value[0] : null);

// Email message interface for the component

// Initialize emailMessages before the watch function
const emailMessages = ref<EmailMsg[]>([]);

// Watch for changes in emails prop and update emailMessages accordingly
watch(
  emails,
  (newEmails) => {
    if (newEmails && newEmails.length > 0) {
      // Convert Email objects to EmailWithBody format for display
      emailMessages.value = newEmails.map((email) => ({
        id: email.id,
        threadId: email.thread_id,
        from: email.from,
        to: email.to,
        cc: email.cc,
        bcc: email.bcc,
        date: email.date,
        subject: email.subject,
        snippet: email.snippet,
        body: email.snippet || "No content available", // Use snippet as fallback
        attachments: email.attachments
          ? typeof email.attachments === "string"
            ? JSON.parse(email.attachments)
            : email.attachments
          : [],
      }));
    }
  },
  { immediate: true }
);

const draft = computed<Draft | undefined>(() => {
  return drafts.value.find((d) => d.parent_email_id === email.value?.id);
});

// Check if current email can be snoozed
const canSnoozeEmail = computed(() => {
  if (!email.value) return false;

  // Check if email's storage_location is in snoozable categories
  return !!email.value.storage_location && SNOOZABLE_CATEGORIES.includes(email.value.storage_location as any);
});

const autoReply = ref("");
const bodyHtml = ref("");
const localAccessToken = ref(props.access_token);

// Remove fake attachments logic - now using real attachments from Gmail API

watch(email, async () => {
  bodyHtml.value = "";
  await fetchThreadEmails();
});

const fetchTry = ref(0);
const maxRetries = 3; // Add retry limit to prevent infinite loops

const decodeBase64Utf8 = (base64: any) => {
  // Gmail encodes with URL-safe base64 (using - and _), so convert first
  base64 = base64.replace(/-/g, "+").replace(/_/g, "/");

  // decode to binary string
  const binaryString = atob(base64);

  // convert binary string to Uint8Array
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // decode UTF-8 bytes to string
  return new TextDecoder("utf-8").decode(bytes);
};

const extractHeader = (headers: any, name: string) => {
  return headers.find((h: any) => h.name.toLowerCase() === name.toLowerCase())?.value || "";
};

const fetchThreadEmails = async (retryCount = 0) => {
  const threadId = props.threadId;
  const accessToken = localAccessToken.value;

  // Prevent infinite retries
  if (retryCount >= maxRetries) {
    console.error("Max retries reached for fetching thread emails");
    return;
  }

  fetchTry.value++;

  try {
    const response = await fetch(`https://www.googleapis.com/gmail/v1/users/me/threads/${threadId}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      if (response.status === 401 && retryCount < maxRetries - 1) {
        const newAccessToken = await refreshAccessToken();
        if (newAccessToken) {
          localAccessToken.value = newAccessToken;
          return fetchThreadEmails(retryCount + 1);
        } else {
          console.error("Failed to refresh token");
          return;
        }
      } else {
        console.error("Failed to fetch thread:", response.status);
        return;
      }
    }

    const data = await response.json();
    console.log("🚀 ~ fetchThreadEmails ~ data:", data);
    const messages = data.messages;

    const getMessages = messages.map((msg: GmailMessage) => {
      const headers = msg.payload?.headers;
      const bodyPart = findHtmlPart(msg.payload);
      console.log("🚀 ~ fetchThreadEmails ~ bodyPart:", bodyPart);

      let decodedHtml = "";

      if (bodyPart?.body?.data) {
        decodedHtml = decodeBase64Utf8(bodyPart.body.data);
        console.log("🚀 ~ fetchThreadEmails ~ decodedHtml:", decodedHtml);
      } else if (msg.payload?.body?.data) {
        decodedHtml = decodeBase64Utf8(msg.payload.body.data);
        console.log("🚀 ~ fetchThreadEmails ~ decodedHtml:", decodedHtml);
      } else {
        decodedHtml = "(No HTML body)";
      }

      const cleanedHtml = cleanGmailReply(decodedHtml);

      // Extract attachments for this specific message
      const attachments: string[] = [];
      if (msg.payload?.parts) {
        msg.payload.parts.forEach((part: any) => {
          if (part.filename && part.filename.trim() !== "") {
            attachments.push(part.filename);
          }
          // Check nested parts for attachments
          if (part.parts) {
            part.parts.forEach((nestedPart: any) => {
              if (nestedPart.filename && nestedPart.filename.trim() !== "") {
                attachments.push(nestedPart.filename);
              }
            });
          }
        });
      }

      const parsedEmail: EmailMsg = {
        id: msg.id,
        from: extractHeader(headers, "From"),
        to: extractHeader(headers, "To"),
        cc: extractHeader(headers, "Cc"),
        bcc: extractHeader(headers, "Bcc"),
        date: extractHeader(headers, "Date"),
        subject: extractHeader(headers, "Subject"),
        snippet: msg.snippet,
        body: cleanedHtml,
        attachments: attachments,
      };

      return parsedEmail;
    });
    emailMessages.value = getMessages;
  } catch (error) {
    console.error("Error fetching thread:", error);
  }
};

function findHtmlPart(part: any): any {
  if (!part) return null;
  if (part.mimeType === "text/html" && part.body?.data) return part;
  if (part.parts) {
    return (
      part.parts.find((sub: any) => sub.mimeType === "text/html" && sub.body?.data) ||
      part.parts.find((sub: any) => sub.mimeType === "text/plain" && sub.body?.data)
    );
  }
  // if (part.mimeType === "text/plain" && part.body?.data) return part;
  return null;
}

function cleanGmailReply(html: string): string {
  const $ = cheerio.load(html);
  $(".gmail_quote,.gmail_quote_container").remove();
  $("blockquote[type='cite']").remove();

  return $.html()?.trim() ?? "";
}

// Function to refresh the access token
async function refreshAccessToken() {
  return await emailCommands.getFreshAccessToken();
}

// Function to retrieve the updated access token from the store
async function getUpdatedAccessToken() {
  const user = currentUserStore.currentUser;
  return user ? user.access_token : null;
}

async function getThreadDrafts(id: string) {
  const d = await invoke<Draft[]>("thread_waiting_drafts", { threadId: id });
  drafts.value = d ?? [];
}

async function getThreadEmails(id: string) {
  const emails = await invoke<Email[]>("list_thread_emails", { threadId: id });
  //  console.log("Thread emails ==>", emails);
  if (emails.length > 0) {
    await getThreadDrafts(id);
    threadEmails.value = emails;
    email.value = emails[0];
  }
}

function onScroll(e: any) {
  const y = e.target.scrollTop;
  const scrollHeight = e.target.scrollHeight;
  const clientHeight = e.target.clientHeight;
  const atBottom = y + clientHeight >= scrollHeight - 1;
  showGoToBottom.value = !atBottom;
  //  console.log("Element Scroll Top:", y, atBottom);
}

const scrollToBottom = () => {
  const el = document.getElementById("html-container");
  if (el) {
    el.scrollTo({
      top: el.scrollHeight,
      behavior: "smooth",
    });
  }
};

async function sendDraft(draft: Draft) {
  //  console.log("Sending email");
  invoke("reply_email", {
    originalMessageId: draft.parent_email_id,
    recipient: draft.to,
    subject: draft.subject,
    body: draft.body,
    attachments: [],
  })
    .then(async () => {
      await invoke("draft_status", { draftId: draft.id, status: "accepted" });
      //  console.log("Draft Accepted");
    })
    .catch((e) => {
      console.error("Error in sending email", e);
    });
}

async function refuseDraft(draft: Draft) {
  //  console.log("Refusing email");
  await invoke("draft_status", { draftId: draft.id, status: "accepted" });
  //  console.log("Draft Refused");
}

// Fetch the email when the component is mounted
onMounted(async () => {
  await fetchThreadEmails();
  document.body.classList.add("overflow-hidden");
  //  console.log("Email", email.value);
  //  console.log("Emails =>", props.emails);
  const id = email.value?.thread_id ?? props.threadId;
  if (id) await getThreadEmails(id);
});

onUpdated(() => {
  // Removed fetchThreadEmails() call to prevent infinite loop
  // fetchThreadEmails() should only be called when needed, not on every update
  document.body.classList.add("overflow-hidden");
});

const getAutoReply = async () => {
  try {
    const response = await fetch("http://localhost:5001/generate_reply", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ content: email.value?.snippet }),
    });
    const data = await response.json();
    autoReply.value = data.reply;
  } catch (error) {
    console.error("Error generating auto reply:", error);
  }
};

const markAsUnread = async () => {
  try {
    await invoke("mark_email_as_unread_in_gmail", { emailId: email.value?.id });
    console.log("Email successfully marked as unread in Gmail.");
  } catch (error) {
    console.error("Failed to mark email as unread in Gmail:", error);
  }
};

const deleteEmail = async () => {
  try {
    await invoke("delete_email_from_gmail", { emailId: email.value?.id, accessToken: await getUpdatedAccessToken() });
    console.log("Email successfully deleted from Gmail.");
  } catch (error) {
    console.error("Failed to delete email from Gmail:", error);
  }
};

const archiveEmail = async () => {
  // Implementation for archiving email
  console.log("Archive email functionality to be implemented");
};

const openSnoozeModal = () => {
  showSnoozeModal.value = true;
};

const currentEmailCategoryStore = useCurrentEmailCategoryStore();

const handleEmailSnoozed = async (threadId: string) => {
  console.log("Email snoozed:", threadId);

  // Emit a global event that the email was snoozed
  await emit("email-snoozed", { threadId });

  // Switch to snoozed emails category
  const snoozedCategory = {
    id: "snoozed_emails_category",
    category_id: "snoozed_emails",
    name: "Snoozed Emails",
    sender_company: "Snoozed Emails",
    created_at: new Date(),
    description: "Emails that have been snoozed",
    priority: 1,
    color: "#f97316", // orange color
  };

  currentEmailCategoryStore.selectEmailCategory("snoozed_emails", snoozedCategory);

  // Close the email detail view
  emitEvent("close");
};

// Email action handlers
const replyEmail = () => {
  console.log("Reply functionality to be implemented");
};

const replyAllEmail = () => {
  console.log("Reply All functionality to be implemented");
};

const forwardEmail = () => {
  console.log("Forward functionality to be implemented");
};

// New message action functions
const starMessage = (message: any) => {
  console.log("Star message:", message);
  // Handle star functionality here
};

const archiveMessage = (message: any) => {
  console.log("Archive message:", message);
  // Handle archive functionality here
};

const deleteMessage = (message: any) => {
  console.log("Delete message:", message);
  // Handle delete functionality here
};

const handleFileUpload = (files: FileList) => {
  console.log("Files uploaded:", files);
  // Handle file upload logic here
};

const editDraft = (draft: Draft) => {
  console.log("Edit draft:", draft);
  // Handle draft editing logic here
};

const useAutoReply = () => {
  console.log("Use auto reply:", autoReply.value);
  // Handle using the auto reply
};
</script>

<style scoped>
/* Additional styling to improve the layout */

/* This class will disable scrolling on the body */
</style>
