export interface EmailSummaries {
  id: string;
  userId: string;
  subject: string;
  snippet: string;
  threadId: string;
  date: string; // ISO 8601 format, e.g. "2025-03-10T13:19:18Z"
  category: string;
  isRead: boolean;
  direction: 'received' | 'sent'; // Assuming direction is either of these two
  shard: string;
  email_intents: string[]; // could be refined if values are fixed
  recommended_actions: string[];
  urgency_score: number;
  actionable_items: any[]; // if you know the structure of items, replace `any` with that type
}
