use diesel::{
    query_dsl::methods::FilterDsl, BoolExpressionMethods, ExpressionMethods, RunQueryDsl,
};
use std::{thread::sleep, time::Duration};

use diesel::result::Error;
use diesel::Connection;

use crate::{
    db::establish_db_connection,
    db::get_pooled_connection,
    models::draft::{self, Draft},
    schema::drafts,
        schema::drafts::dsl,

};

pub fn store_draft(new_draft: &Draft) -> Draft {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    let id = new_draft.id.clone();
    diesel::insert_into(drafts::table)
        .values(new_draft)
        .execute(connection)
        .expect("Error inserting new Draft");

    drafts::table
        .filter(drafts::id.eq(id))
        .first(connection)
        .expect("Error fetching newly created draft")
}

pub fn get_draft_task(task_id: i32) -> Option<Draft> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    drafts::table
        .filter(drafts::task_id.eq(task_id)) // Fetch the task with the given task ID
        .first::<Draft>(connection)
        .ok() // Convert result into Option<Task>, returning None if not found
}

pub fn delete_all_draft() -> Result<usize, Error> {
    // let connection = &mut establish_db_connection();
    let connection: &mut r2d2::PooledConnection<diesel::r2d2::ConnectionManager<diesel::SqliteConnection>> = &mut get_pooled_connection();

    connection.transaction::<_, Error, _>(|conn| {
        // Delete all meetings from the meetings table
        let deleted_rows = diesel::delete(dsl::drafts).execute(conn).map_err(|err| {
            eprintln!("Error deleting all meetings: {}", err);
            err
        })?;

        Ok(deleted_rows)
    })
}
pub fn get_thread_drafts(thread_id: String) -> Vec<Draft> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    drafts::table
        .filter(drafts::thread_id.eq(thread_id)) // Fetch the task with the given task ID
        .load::<Draft>(connection)
        .expect("Error getting thread drafts !!")
    // Convert result into Option<Task>, returning None if not found
}

// pub fn get_waiting_drafts() -> Vec<Draft> {
//     // let connection = &mut establish_db_connection();
//     let  connection = &mut get_pooled_connection();

//     drafts::table
//         .filter(drafts::status.eq("waiting_user_action")) // Fetch the task with the given task ID
//         .load::<Draft>(connection)
//         .expect("Error getting thread drafts !!")
//     // Convert result into Option<Task>, returning None if not found
// }

pub fn get_waiting_drafts() -> Vec<Draft> {
    let mut attempts = 0;
    let max_attempts = 5;
    let mut delay = 100; // milliseconds

    loop {
        let mut connection = get_pooled_connection();

        let result = drafts::table
            .filter(drafts::status.eq("waiting_user_action"))
            .load::<Draft>(&mut connection);

        match result {
            Ok(drafts) => return drafts,
            Err(Error::DatabaseError(_, ref info)) if info.message().contains("locked") => {
                attempts += 1;
                if attempts >= max_attempts {
                    panic!("❌ Database still locked after {max_attempts} attempts Inside get_waiting_drafts.");
                }
                println!(
                    "⚠️ Database is locked, attempt {}/{}. Inside get_waiting_drafts Retrying in {}ms...",
                    attempts, max_attempts, delay
                );
                sleep(Duration::from_millis(delay));
                delay *= 2;
            }
            Err(e) => panic!("❌ Unexpected DB error: {:?}", e),
        }
    }
}

// pub fn get_thread_waiting_drafts(thread_id: String) -> Vec<Draft> {
//     // let connection = &mut establish_db_connection();
//     let  connection = &mut get_pooled_connection();

//     drafts::table
//         .filter(drafts::thread_id.eq(thread_id).and(drafts::status.eq("waiting_user_action"))) // Fetch the task with the given task ID
//         .load::<Draft>(connection)
//         .expect("Error getting thread drafts !!")
//     // Convert result into Option<Task>, returning None if not found
// }

pub fn get_thread_waiting_drafts(thread_id: String) -> Vec<Draft> {
    let mut attempts = 0;
    let max_attempts = 5;
    let mut delay = 100; // milliseconds

    loop {
        let mut connection = get_pooled_connection();

        match drafts::table
            .filter(
                drafts::thread_id
                    .eq(&thread_id)
                    .and(drafts::status.eq("waiting_user_action")),
            )
            .load::<Draft>(&mut connection)
        {
            Ok(drafts) => return drafts,
            Err(Error::DatabaseError(_, info)) if info.message().contains("locked") => {
                attempts += 1;
                if attempts >= max_attempts {
                    panic!("❌ Still locked after {} attempts", max_attempts);
                }
                println!(
                    "⚠️ Database is locked, attempt {}/{}. Retrying in {}ms...",
                    attempts, max_attempts, delay
                );
                sleep(Duration::from_millis(delay));
                delay *= 2; // exponential backoff
            }
            Err(e) => panic!("❌ Unexpected DB error: {:?}", e),
        }
    }
}

pub fn update_draft_status(draft_id: String, status: &str) {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    diesel::update(drafts::table.filter(drafts::id.eq(draft_id)))
        .set(drafts::status.eq(status))
        .execute(connection)
        .expect("Error updating draft status");
}

pub fn get_draft(draft_id: String) -> Option<Draft> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    drafts::table
        .filter(drafts::id.eq(draft_id)) // Fetch the task with the given task ID
        .first::<Draft>(connection)
        .ok() // Convert result into Option<Task>, returning None if not found
}
