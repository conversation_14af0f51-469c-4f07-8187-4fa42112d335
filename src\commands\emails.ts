import { invoke } from "@tauri-apps/api/core";
import { EmailMessage } from "../components/chat/email-page-view/email.types";
import { toast } from "vue-sonner";


async function getFreshAccessToken() {
  try {
    return await invoke<string>("get_fresh_access_token");
  } catch (error) {
    console.error(error);
    return null;
  }
}

async function cleanupOldEmailsFromTodaysCategories(): Promise<number> {
  try {
    const result = await invoke<number>("cleanup_old_emails_from_today_categories");
    console.log(`🧹 Successfully cleaned up ${result} old emails from today's categories`);
    return result;
  } catch (error) {
    console.error("❌ Failed to cleanup old emails:", error);
    throw error;
  }
}

export interface SendEmail {
  recipient: string,
  cc?: string,
  bcc?: string,
  subject: string,
  body: string,
  attachments: string[],
  inReplyTo?: string,
  references?: string,
  threadId?: string
}

export interface ReplyResponse {
  messages: {
    tone: string,
    value: string
  }[];
}

async function sendEmail(email: SendEmail) {
  try {
    await invoke("send_email", email as any);
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
}

async function replyEmailFromAction(thread: EmailMessage[], action: string) {
  try {
    return await invoke<ReplyResponse>("generate_ai_action_reply", { thread, action });
  } catch (error) {
    console.error(error);
    toast.error("Failed to generate reply");
    return null;
  }
}


const emailCommands = {
  getFreshAccessToken,
  cleanupOldEmailsFromTodaysCategories,
  sendEmail,
  replyEmailFromAction
};

export default emailCommands;
