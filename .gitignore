# ========================
# Vue3 (Vite / Webpack)
# ========================
node_modules/
dist/
build/
*.local
dist-ssr


# ========================
# Tauri (Rust backend)
# ========================
/src-tauri/target/
src-tauri/Cargo.lock

# If you use release builds
/src-tauri/target/release/
/src-tauri/target/debug/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*


# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

async_progress

# ========================
# User-specific config
# ========================
/.config/oway/email_fetch_progress.json
*.sqlite
*.db
*.bak
*.tmp

# Global ignore for OS X/Linux user config
**/.config/oway/email_fetch_progress.json

# ========================
# Optional Git + Deployment
# ========================
*.tgz
*.zip
*.tar
.yarn/*
.pnpm-debug.log
npm-debug.log*

# ========================
# Electron-ish / Tauri Misc
# ========================
*.asar
*.node
.DS_Store
*.swp
