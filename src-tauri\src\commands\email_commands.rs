use std::path::PathBuf;
use std::sync::Arc;

use crate::email_generator::dto::{ EmailMessage, ReplyAiResponse };
use crate::email_generator::generator_entry::ai_generate_email_reply_from_action;
use crate::models::app_data::AppData;
use crate::services::emails_service::{ self, EmailsOutput, Filters };
use crate::{ models::email::Email, services::emails_service::SearchQuery };
use chrono::{ DateTime, NaiveDateTime, Utc };
use tokio::sync::RwLock;
use uuid::Uuid;

use super::delete_email::reply_to_email;

#[tauri::command]
pub fn list_emails(
    category_id: String,
    full_child_domains: String,
    page: usize,
    per_page: usize,
    search_query: Option<SearchQuery>,
    filters: Option<Filters>
) -> EmailsOutput {
    // Debug logs for input parameters
    // println!("Category ID: {}", category_id);
    // println!("Full Child Domains: {}", full_child_domains);
    // println!("Page: {}", page);
    // println!("Per Page: {}", per_page);

    // Call the service function
    emails_service
        ::list_emails(&category_id, &full_child_domains, page, per_page, search_query, filters)
        .expect("Failed to load emails")
}

#[tauri::command]
pub fn list_thread_emails(thread_id: String) -> Vec<Email> {
    emails_service::get_emails_by_thread_id(thread_id).expect("Failed to get thread emails !")
}

#[tauri::command]
pub fn cleanup_old_emails_from_today_categories() -> Result<usize, String> {
    emails_service
        ::cleanup_old_emails_from_today_categories()
        .map_err(|e| format!("Failed to cleanup old emails: {:?}", e))
}

#[tauri::command]
pub fn list_emails_with_process_flag_true() -> Vec<Email> {
    emails_service::list_emails_with_process_flag().expect("Failed to load emails")
}

#[tauri::command]
pub fn delete_emails(email_ids: Vec<String>) {
    for email_id in email_ids {
        match emails_service::delete_emails(email_id) {
            Ok(_) => println!("Successfully deleted email."),
            Err(err) => eprintln!("Failed to delete email: {}", err),
        }
    }
}

#[tauri::command]
pub fn delete_email(email_id: String) {
    match emails_service::delete_emails(email_id) {
        Ok(_) => println!("Successfully deleted email."),
        Err(err) => eprintln!("Failed to delete email: {}", err),
    }
}

#[tauri::command]
pub fn new_email(
    subject: String,
    snippet: String,
    from: String,
    to: Vec<String>,
    cc: Option<Vec<String>>,
    bcc: Option<Vec<String>>,
    date: NaiveDateTime,
    category: Option<String>,
    labels: Option<Vec<String>>,
    attachments: Option<Vec<String>>,
    metadata_headers: Option<Vec<String>>,
    email_body_url: Option<String>, // Field for email body URL
    is_read: Option<bool>, // Field for read status
    thread_id: Option<String>, // Field for thread ID
    thread_summary: Option<String>, // Field for thread summary
    priority: Option<String>, // Field for priority
    urgency_score: Option<i32>, // Field for urgency score
    sentiment: Option<String>, // Field for sentiment
    actionable_items: Option<Vec<String>>, // Field for actionable items
    language: Option<String>, // Field for detected language
    phishing_risk: Option<String>, // Field for phishing risk
    sender_reputation: Option<String>, // Field for sender reputation
    full_domain: Option<String>, // Field for full domain
    main_domain: Option<String>, // Field for main domain
    storage_location: Option<String>, // Field for storage location
    is_flagged: Option<bool>, // Field for flagged status
    process_flag: Option<bool>, // Field for processing flag
    email_type: Option<String>, // Field for email type
    is_thread_root: Option<bool>, // Field for thread root status
    received_as: Option<String>, // Field for how the email was received
    parent_email_id: Option<String>, // Field for parent email ID
    read_receipt_url: Option<String>, // Field for read receipt URL
    reply_suggestion: Option<String>, // Field for reply suggestions (JSON)
    follow_up_date: Option<NaiveDateTime>, // Field for follow-up date
    meeting_proposed: Option<bool>, // Field for meeting proposal
    meeting_link: Option<String>, // Field for meeting link
    is_delegated: Option<bool>, // Field for delegation status
    task_status: Option<String>, // Field for task status
    auto_reply_sent: Option<bool>, // Field for auto-reply status
    flagged_keywords: Option<Vec<String>>, // Field for flagged keywords
    attachments_downloaded: Option<bool>, // Field for downloaded attachment status
    shared_with: Option<Vec<String>>, // Field for shared recipients (JSON)
    analytics_score: Option<i32>, // Field for analytics score
    response_time: Option<i32>, // Field for response time
    ai_generated: Option<bool>, // Field for AI-generated content
    is_send: Option<bool>, // Field for AI-generated content
    source_app: Option<String> // Field for source app
) -> Email {
    let email = Email {
        id: Uuid::new_v4().to_string(),
        subject,
        snippet,
        from,
        to: serde_json::to_string(&to).unwrap(), // Convert Vec<String> to JSON string
        cc: cc.map(|c| serde_json::to_string(&c).unwrap()), // Convert Vec<String> to JSON string
        bcc: bcc.map(|b| serde_json::to_string(&b).unwrap()), // Convert Vec<String> to JSON string
        date,
        category,
        labels: labels.map(|l| serde_json::to_string(&l).unwrap()), // Convert Vec<String> to JSON string
        attachments: attachments.clone().map(|a| serde_json::to_string(&a).unwrap()), // Convert Vec<String> to JSON string
        attachment_types: attachments.clone().map(|a| a.join(", ")), // Join attachment types as CSV
        total_attachment_size: Some(attachments.as_ref().map_or(0, |a| a.len() as i32)), // Calculate size
        metadata_headers: metadata_headers.map(|mh| serde_json::to_string(&mh).unwrap()), // Convert Vec<String> to JSON string
        email_body_url, // Assign email body URL directly
        is_read, // Assign read status
        thread_id, // Assign thread ID
        thread_summary, // Assign thread summary
        priority, // Assign priority
        urgency_score, // Assign urgency score
        sentiment, // Assign sentiment
        actionable_items: actionable_items.map(|ai| serde_json::to_string(&ai).unwrap()), // Convert Vec<String> to JSON string
        language, // Assign language
        phishing_risk, // Assign phishing risk
        sender_reputation, // Assign sender reputation
        full_domain, // Assign full domain
        main_domain, // Assign main domain
        storage_location, // Assign storage location
        is_flagged, // Assign flagged status
        process_flag, // Default to false if not provided
        email_type, // Assign email type
        is_thread_root, // Assign thread root status
        received_as, // Assign received as
        parent_email_id, // Assign parent email ID
        read_receipt_url, // Assign read receipt URL
        reply_suggestion, // Assign reply suggestion (JSON string)
        follow_up_date, // Assign follow-up date
        meeting_proposed, // Assign meeting proposal status
        meeting_link, // Assign meeting link
        is_delegated, // Assign delegation status
        task_status, // Assign task status
        auto_reply_sent, // Assign auto-reply status
        flagged_keywords: flagged_keywords.map(|fk| serde_json::to_string(&fk).unwrap()), // Convert Vec<String> to JSON string
        attachments_downloaded, // Assign downloaded attachment status
        shared_with: shared_with.map(|sw| serde_json::to_string(&sw).unwrap()), // Convert Vec<String> to JSON string
        analytics_score, // Assign analytics score
        response_time, // Assign response time
        ai_generated,
        is_send, // Assign AI-generated content status
        source_app, // Assign source app
        created_at: Some(chrono::Utc::now().naive_utc()), // Automatically assign creation timestamp
        updated_at: None, // Initially, no update timestamp
    };

    emails_service::store_new_email(&email);

    email
}

#[tauri::command]
pub async fn reply_email(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>,
    original_message_id: String,
    recipient: String,
    cc: Option<String>,
    bcc: Option<String>,
    subject: String,
    body: String,
    attachments: Vec<PathBuf>
) -> Result<(), String> {
    reply_to_email(
        app_data,
        original_message_id,
        recipient,
        cc,
        bcc,
        subject,
        body,
        attachments
    ).await;

    Ok(())
}

#[tauri::command]
pub async fn get_fresh_access_token(
    app_data: tauri::State<'_, Arc<RwLock<AppData>>>
) -> Result<String, String> {
    Ok(
        emails_service
            ::get_fresh_access_token(app_data).await
            .expect("Error getting a fresh access token!")
    )
}

#[tauri::command]
pub fn mark_emails_as_read(email_ids: Vec<String>) -> Result<(), String> {
    emails_service
        ::mark_emails_as_read(email_ids)
        .map_err(|e| format!("Failed to mark emails as read: {}", e))
}

#[tauri::command]
pub fn mark_emails_as_unread(email_ids: Vec<String>) -> Result<(), String> {
    emails_service
        ::mark_emails_as_unread(email_ids)
        .map_err(|e| format!("Failed to mark emails as unread: {}", e))
}

#[tauri::command]
pub fn recalculate_category_unread_counts() -> Result<(), String> {
    emails_service
        ::recalculate_category_unread_counts()
        .map_err(|e| format!("Failed to recalculate category unread counts: {}", e))
}

#[tauri::command]
pub async fn generate_ai_action_reply(
    thread: Vec<EmailMessage>,
    action: String
) -> Result<ReplyAiResponse, String> {
    match ai_generate_email_reply_from_action(thread, &action).await {
        Ok(response) => Ok(response),
        Err(err) => Err(format!("Failed to generate AI reply: {:?}", err)),
    }
}
