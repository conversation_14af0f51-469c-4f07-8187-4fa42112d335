// Function	Description
// enqueue_task	Adds a task to the queue with priority-based positioning
// get_next_task	Retrieves the highest priority task in the queue
// complete_task	Marks a task as completed and removes it from the queue
// dequeue_task	Removes a task from the queue without marking it as completed
// reschedule_task	Moves a blocked task to a later execution time
// auto_execute_task	Auto-completes AI-capable tasks
// assign_task_to_agent	Assigns a task to an available agent to balance workload
// clear_completed_tasks	Deletes all completed tasks from the database
// get_queue_count	Returns the number of tasks currently in the queue
use crate::schema::subtasks::dsl as subtask_dsl;
use crate::schema::tasks::dsl as task_dsl;
use crate::{
    db::establish_db_connection,
    db::get_pooled_connection,
    models::task::{NewSubtask, NewTask, Subtask, Task, TaskStats},
    schema::{assistants::table, subtasks, tasks},
        schema::tasks::dsl,

};
use diesel::Connection;

use chrono::NaiveDateTime;
use chrono::Utc;
use diesel::dsl::now;
use diesel::dsl::sql;
use diesel::prelude::*;
use diesel::result::Error as DieselError;
use diesel::sql_types::Nullable;
use diesel::sql_types::Text;
use reqwest::Client;
use serde_json::json;
use std::{thread::sleep, time::Duration};


pub fn list_tasks() -> Vec<Task> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    tasks::table
        .order_by(task_dsl::created_at.desc()) // Fetch tasks in descending order of creation
        .load::<Task>(connection)
        .expect("Error retrieving tasks")
}

pub fn list_completed_tasks() -> Vec<Task> {
    println!("$ Getting completed tasks");

    let mut attempts = 0;
    let max_attempts = 5;
    let mut delay = 100; // milliseconds

    loop {
        let mut connection = get_pooled_connection();

        let result = tasks::table
            .filter(
                tasks::execution_status
                    .eq("completed")
                    .or(tasks::execution_status.eq("failed")),
            )
            .order_by(task_dsl::created_at.desc())
            .load::<Task>(&mut connection);

        match result {
            Ok(tasks) => return tasks,
            Err(DieselError::DatabaseError(_, ref info)) if info.message().contains("locked") => {
                attempts += 1;
                if attempts >= max_attempts {
                    panic!(
                        "❌ Database still locked after {} attempts Inside list_completed_tasks ",
                        max_attempts
                    );
                }
                println!(
                    "⚠️ Database is locked, attempt {}/{}. Inside list_completed_tasks Retrying in {}ms...",
                    attempts, max_attempts, delay
                );
                sleep(Duration::from_millis(delay));
                delay *= 2; // exponential backoff
            }
            Err(e) => panic!("❌ Unexpected DB error: {:?}", e),
        }
    }
}

pub fn delete_all_tasks() -> Result<usize, DieselError> {
    // let connection = &mut establish_db_connection();
    let connection: &mut r2d2::PooledConnection<diesel::r2d2::ConnectionManager<diesel::SqliteConnection>> = &mut get_pooled_connection();

    connection.transaction::<_, DieselError, _>(|conn| {
        // Delete all meetings from the meetings table
        let deleted_rows = diesel::delete(dsl::tasks).execute(conn).map_err(|err| {
            eprintln!("Error deleting all meetings: {}", err);
            err
        })?;

        Ok(deleted_rows)
    })
}

// pub fn list_completed_tasks() -> Vec<Task> {
//     println!("$ Getting completed tasks");
//     // let connection = &mut establish_db_connection();
//     let  connection = &mut get_pooled_connection();

//     tasks::table
//         .filter(tasks::execution_status.eq("completed").or(tasks::execution_status.eq("failed")))
//         .order_by(task_dsl::created_at.desc()) // Fetch tasks in descending order of creation
//         .load::<Task>(connection)
//         .expect("Error retrieving tasks")
// }

// pub fn list_tasks_in_queue() -> Vec<Task> {
//     println!("$ Getting queued tasks");
//     // let connection = &mut establish_db_connection();
//     let  mut connection = get_pooled_connection();

//     tasks::table
//         .filter(tasks::execution_status.ne("completed").and(tasks::execution_status.ne("failed")))
//         .order_by(task_dsl::created_at.desc()) // Fetch tasks in descending order of creation
//         .load::<Task>(&mut *connection)
//         .expect("Error retrieving tasks")
// }

pub fn list_tasks_in_queue() -> Vec<Task> {
    println!("$ Getting queued tasks");

    let mut attempts = 0;
    let max_attempts = 5;
    let mut delay = 100; // in milliseconds

    loop {
        let mut connection = get_pooled_connection();

        let result = tasks::table
            .filter(
                tasks::execution_status
                    .ne("completed")
                    .and(tasks::execution_status.ne("failed")),
            )
            .order_by(task_dsl::created_at.desc())
            .load::<Task>(&mut connection);

        match result {
            Ok(tasks) => return tasks,
            Err(DieselError::DatabaseError(_, ref info)) if info.message().contains("locked") => {
                attempts += 1;
                if attempts >= max_attempts {
                    panic!("❌ Database still locked after {max_attempts} attempts.");
                }
                println!(
                    "⚠️ Database is locked, attempt {}/{}. Retrying in {}ms...",
                    attempts, max_attempts, delay
                );
                sleep(Duration::from_millis(delay));
                delay *= 2; // exponential backoff
            }
            Err(e) => panic!("❌ Unexpected DB error: {:?}", e),
        }
    }
}

pub fn list_subtasks(task_id: i32) -> Vec<Subtask> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    subtasks::table
        .filter(subtask_dsl::parent_task_id.eq(task_id)) // Fetch only subtasks belonging to the given task ID
        .order_by(subtask_dsl::created_at.desc()) // Fetch subtasks in descending order of creation
        .load::<Subtask>(connection)
        .expect("Error retrieving subtasks")
}

pub fn get_task(task_id: i32) -> Option<Task> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    tasks::table
        .filter(task_dsl::id.eq(task_id)) // Fetch the task with the given task ID
        .first::<Task>(connection)
        .ok() // Convert result into Option<Task>, returning None if not found
}

pub fn get_subtask(subtask_id: i32) -> Option<Subtask> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    subtasks::table
        .filter(subtask_dsl::id.eq(subtask_id)) // Fetch the subtask with the given subtask ID
        .first::<Subtask>(connection)
        .ok() // Convert result into Option<Subtask>, returning None if not found
}

pub fn store_task(new_task: &NewTask) -> Task {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();
    // let stored = store_task(&new_task.clone());

    diesel::insert_into(tasks::table)
        .values(new_task)
        .execute(connection)
        .expect("Error inserting new task");

    tasks::table
        .order(tasks::id.desc())
        .first(connection)
        .expect("Error fetching newly created task")
}

pub fn update_task_execution_status(task_id: i32, execution_status: &str) {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    diesel::update(tasks::table.filter(tasks::id.eq(task_id)))
        .set(tasks::execution_status.eq(execution_status))
        .execute(connection)
        .expect("Error updating queue execution status");
}

pub fn update_task_status(task_id: i32, status: &str) {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    diesel::update(tasks::table.filter(tasks::id.eq(task_id)))
        .set(tasks::status.eq(status))
        .execute(connection)
        .expect("Error updating queue execution status");
}

/// Enqueue a task or subtask with a queue position based on priority
pub fn enqueue_task(task_id: i32, is_subtask: bool) {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    let (priority_str, due_date): (Option<String>, Option<String>) = if is_subtask {
        subtasks::table
            .filter(subtasks::id.eq(task_id))
            .select((subtasks::priority, subtasks::due_date))
            .first(connection)
            .expect("Error retrieving priority and due date")
    } else {
        tasks::table
            .filter(tasks::id.eq(task_id))
            .select((tasks::priority, tasks::due_date))
            .first(connection)
            .expect("Error retrieving priority and due date")
    };

    // Handle null values safely
    let priority = match priority_str.as_deref() {
        Some("urgent") => 1,
        Some("high") => 2,
        Some("medium") => 3,
        _ => 4, // Default priority for unknown or null values
    };

    let max_position: Option<i32> = if is_subtask {
        subtasks::table
            .select(diesel::dsl::max(subtasks::queue_position))
            .first(connection)
            .expect("Error retrieving max queue position")
    } else {
        tasks::table
            .select(diesel::dsl::max(tasks::queue_position))
            .first(connection)
            .expect("Error retrieving max queue position")
    };

    let new_position = max_position.unwrap_or(0) + priority;

    if is_subtask {
        diesel::update(subtasks::table.filter(subtasks::id.eq(task_id)))
            .set((
                subtasks::queue_position.eq(new_position),
                subtasks::status.eq("in_progress"),
            ))
            .execute(connection)
            .expect("Error updating queue position");
    } else {
        diesel::update(tasks::table.filter(tasks::id.eq(task_id)))
            .set((
                tasks::queue_position.eq(new_position),
                tasks::status.eq("in_progress"),
            ))
            .execute(connection)
            .expect("Error updating queue position");
    }

    if let Some(due) = due_date {
        if is_subtask {
            diesel::update(
                subtasks::table.filter(
                    subtasks::id
                        .eq(task_id)
                        .and(subtasks::snoozed_until.is_null()),
                ),
            )
            .set(
                subtasks::snoozed_until
                    .eq(diesel::dsl::sql(&format!("datetime('{}', '+1 hour')", due))),
            )
            .execute(connection)
            .expect("Error updating snoozed time");
        } else {
            diesel::update(
                tasks::table.filter(tasks::id.eq(task_id).and(tasks::snoozed_until.is_null())),
            )
            .set(
                tasks::snoozed_until
                    .eq(diesel::dsl::sql(&format!("datetime('{}', '+1 hour')", due))),
            )
            .execute(connection)
            .expect("Error updating snoozed time");
        }
    }
}

pub fn store_subtask(new_subtask: &NewSubtask) {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    diesel::insert_into(subtasks::table)
        .values(new_subtask)
        .execute(connection)
        .expect("Error inserting new subtask");
}

/// Delete all subtasks associated with a given task
pub fn delete_task_subtasks(task_id: i32) {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    diesel::delete(subtasks::table.filter(subtask_dsl::parent_task_id.eq(task_id)))
        .execute(connection)
        .expect("Error deleting subtasks for task");
}

/// Delete a task by ID
pub fn delete_task(task_id: i32) {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    diesel::delete(tasks::table.filter(task_dsl::id.eq(task_id)))
        .execute(connection)
        .expect("Error deleting task");
}

/// Delete a specific subtask by its ID
pub fn delete_subtask(subtask_id: i32) {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    diesel::delete(subtasks::table.filter(subtask_dsl::id.eq(subtask_id)))
        .execute(connection)
        .expect("Error deleting subtask");
}

/// Reschedule a task or subtask
pub fn reschedule_task(task_id: i32, is_subtask: bool) {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    let current_time = Utc::now().to_rfc3339();

    if is_subtask {
        diesel::update(subtask_dsl::subtasks.filter(subtask_dsl::id.eq(task_id)))
            .set((
                subtask_dsl::status.eq("rescheduled"),
                subtask_dsl::due_date.eq(current_time),
                subtask_dsl::reschedule_count.eq(subtask_dsl::reschedule_count + 1),
            ))
            .execute(connection)
            .expect("Error rescheduling subtask");
    } else {
        diesel::update(task_dsl::tasks.filter(task_dsl::id.eq(task_id)))
            .set((
                task_dsl::status.eq("rescheduled"),
                task_dsl::due_date.eq(current_time),
                task_dsl::reschedule_count.eq(task_dsl::reschedule_count + 1),
            ))
            .execute(connection)
            .expect("Error rescheduling task");
    }
}

/// Get the next task or subtask to execute
pub fn get_next_task(is_subtask: bool) -> Option<Task> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    if is_subtask {
        subtask_dsl::subtasks
            .filter(subtask_dsl::queue_position.is_not_null())
            .order_by(subtask_dsl::queue_position.asc())
            .first::<Subtask>(connection)
            .ok()
            .map(|subtask| Task {
                id: subtask.id,
                title: subtask.title,
                category: Some("subtask".to_string()),
                sub_type: None,
                status: subtask.status,
                priority: subtask.priority,
                linked_entity: None,
                context_type: None,
                context_data: None,
                due_date: subtask.due_date,
                assigned_to: subtask.assigned_to,
                requires_sync: subtask.requires_sync,
                queue_position: subtask.queue_position,
                snoozed_until: subtask.snoozed_until,
                auto_executable: subtask.auto_executable,
                execution_status: subtask.execution_status,
                execution_attempts: subtask.execution_attempts,
                last_attempted_at: subtask.last_attempted_at,
                reschedule_count: subtask.reschedule_count,
                created_at: subtask.created_at,
            })
    } else {
        task_dsl::tasks
            .filter(task_dsl::queue_position.is_not_null())
            .order_by(task_dsl::queue_position.asc())
            .first::<Task>(connection)
            .ok()
    }
}

/// Get the total count of tasks in the queue
pub fn get_queue_count() -> (i64, i64) {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    let task_count: i64 = task_dsl::tasks
        .filter(task_dsl::queue_position.is_not_null())
        .count()
        .get_result(connection)
        .expect("Error counting tasks");

    let subtask_count: i64 = subtask_dsl::subtasks
        .filter(subtask_dsl::queue_position.is_not_null())
        .count()
        .get_result(connection)
        .expect("Error counting subtasks");

    (task_count, subtask_count)
}

pub fn get_next_subtask() -> Option<Subtask> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    subtasks::table
        .filter(subtask_dsl::queue_position.is_not_null())
        .order_by(subtask_dsl::queue_position.asc())
        .first::<Subtask>(connection)
        .ok()
}

pub fn get_task_stats() -> Result<TaskStats, String> {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    let active_count: i64 = task_dsl::tasks
        .filter(task_dsl::status.eq(Some("in_progress")))
        .count()
        .get_result(connection)
        .unwrap_or(0);

    let pending_count: i64 = task_dsl::tasks
        .filter(task_dsl::status.eq("pending"))
        .count()
        .get_result(connection)
        .unwrap_or(0);

    let ai_executed_count: i64 = task_dsl::tasks
        .filter(task_dsl::status.eq("completed"))
        .filter(task_dsl::assigned_to.eq("AI"))
        .count()
        .get_result(connection)
        .unwrap_or(0);

    let escalated_count: i64 = task_dsl::tasks
        .filter(task_dsl::status.eq("escalated"))
        .count()
        .get_result(connection)
        .unwrap_or(0);

    Ok(TaskStats {
        active: active_count,
        pending: pending_count,
        ai_executed: ai_executed_count,
        escalated: escalated_count,
    })
}
/// Fetch the highest priority task or subtask from the queue, skipping snoozed items
/// Fetch the highest priority task or subtask from the queue, skipping snoozed items

/// Fetch the highest priority task or subtask from the queue, skipping snoozed items
/// Fetch the highest priority task or subtask from the queue, skipping snoozed items

// pub fn get_next_task(is_subtask: bool) -> Option<Task> {
//     let connection = &mut establish_db_connection();

//     if is_subtask {
//         subtask_dsl::subtasks
//             .filter(subtask_dsl::queue_position.is_not_null())
//             .filter(
//                 subtask_dsl::snoozed_until
//                     .is_null()
//                     .or(subtask_dsl::snoozed_until.lt(diesel::dsl::sql("strftime('%Y-%m-%d %H:%M:%S', 'now')"))),
//             )
//             .order_by((subtask_dsl::queue_position.asc(), subtask_dsl::due_date.asc()))
//             .select((
//                 subtask_dsl::id,
//                 subtask_dsl::parent_task_id.nullable(),
//                 subtask_dsl::title,
//                 subtask_dsl::status,
//                 subtask_dsl::priority,
//                 subtask_dsl::assigned_to.nullable(),
//                 subtask_dsl::due_date.nullable(),
//                 subtask_dsl::requires_sync.nullable(),
//                 subtask_dsl::queue_position.nullable(),
//                 subtask_dsl::snoozed_until.nullable(),
//                 subtask_dsl::auto_executable.nullable(),
//                 subtask_dsl::execution_status.nullable(),
//                 subtask_dsl::execution_attempts.nullable(),
//                 subtask_dsl::last_attempted_at.nullable(),
//                 subtask_dsl::reschedule_count.nullable(),
//                 subtask_dsl::created_at.nullable(),
//             ))
//             .first::<(i32, Option<i32>, String, String, String, Option<String>, Option<String>, Option<bool>, Option<i32>, Option<String>, Option<bool>, Option<String>, Option<i32>, Option<String>, Option<i32>, Option<String>)>(connection)
//             .ok()
//             .map(|(
//                 id,
//                 _parent_task_id,
//                 title,
//                 status,
//                 priority,
//                 assigned_to,
//                 due_date,
//                 requires_sync,
//                 queue_position,
//                 snoozed_until,
//                 auto_executable,
//                 execution_status,
//                 execution_attempts,
//                 last_attempted_at,
//                 reschedule_count,
//                 created_at,
//             )| Task {
//                 id: id,
//                 title,
//                 category: "subtask".to_string(), // Default category for subtasks
//                 sub_type: "subtask".to_string(), // Default sub_type for subtasks
//                 status,
//                 priority,
//                 linked_entity: None,
//                 context_type: None,
//                 context_data: None,
//                 due_date,
//                 assigned_to,
//                 requires_sync: requires_sync.unwrap_or(false),
//                 queue_position,
//                 snoozed_until,
//                 auto_executable: auto_executable.unwrap_or(false),
//                 execution_status: execution_status.unwrap_or("not_started".to_string()),
//                 execution_attempts: execution_attempts.unwrap_or(0),
//                 last_attempted_at,
//                 reschedule_count: reschedule_count.unwrap_or(0),
//                 created_at: created_at
//                 .map(|dt| NaiveDateTime::parse_from_str(&dt, "%Y-%m-%d %H:%M:%S").unwrap_or_else(|_| chrono::Utc::now().naive_utc()))
//                 .unwrap_or_else(|| chrono::Utc::now().naive_utc()),
//              })
//     } else  {
//         subtask_dsl::subtasks
//             .filter(subtask_dsl::queue_position.is_not_null())
//             .filter(
//                 subtask_dsl::snoozed_until
//                     .is_null()
//                     .or(subtask_dsl::snoozed_until.lt(diesel::dsl::sql("strftime('%Y-%m-%d %H:%M:%S', 'now')"))),
//             )
//             .order_by((subtask_dsl::queue_position.asc(), subtask_dsl::due_date.asc()))
//             .select((
//                 subtask_dsl::id,
//                 subtask_dsl::parent_task_id.nullable(),
//                 subtask_dsl::title,
//                 subtask_dsl::status,
//                 subtask_dsl::priority,
//                 subtask_dsl::assigned_to.nullable(),
//                 subtask_dsl::due_date.nullable(),
//                 subtask_dsl::requires_sync.nullable(),
//                 subtask_dsl::queue_position.nullable(),
//                 subtask_dsl::snoozed_until.nullable(),
//                 subtask_dsl::auto_executable.nullable(),
//                 subtask_dsl::execution_status.nullable(),
//                 subtask_dsl::execution_attempts.nullable(),
//                 subtask_dsl::last_attempted_at.nullable(),
//                 subtask_dsl::reschedule_count.nullable(),
//                 subtask_dsl::created_at.nullable(),
//             ))
//             .first::<(i32, Option<i32>, String, String, String, Option<String>, Option<String>, Option<bool>, Option<i32>, Option<String>, Option<bool>, Option<String>, Option<i32>, Option<String>, Option<i32>, Option<String>)>(connection)
//             .ok()
//             .map(|(
//                 id,
//                 _parent_task_id,
//                 title,
//                 status,
//                 priority,
//                 assigned_to,
//                 due_date,
//                 requires_sync,
//                 queue_position,
//                 snoozed_until,
//                 auto_executable,
//                 execution_status,
//                 execution_attempts,
//                 last_attempted_at,
//                 reschedule_count,
//                 created_at,
//             )| Task {
//                 id: id,
//                 title,
//                 category: "subtask".to_string(), // Default category for subtasks
//                 sub_type: "subtask".to_string(), // Default sub_type for subtasks
//                 status,
//                 priority,
//                 linked_entity: None,
//                 context_type: None,
//                 context_data: None,
//                 due_date,
//                 assigned_to,
//                 requires_sync: requires_sync.unwrap_or(false),
//                 queue_position,
//                 snoozed_until,
//                 auto_executable: auto_executable.unwrap_or(false),
//                 execution_status: execution_status.unwrap_or("not_started".to_string()),
//                 execution_attempts: execution_attempts.unwrap_or(0),
//                 last_attempted_at,
//                 reschedule_count: reschedule_count.unwrap_or(0),
//                 created_at: created_at
//                 .map(|dt| NaiveDateTime::parse_from_str(&dt, "%Y-%m-%d %H:%M:%S").unwrap_or_else(|_| chrono::Utc::now().naive_utc()))
//                 .unwrap_or_else(|| chrono::Utc::now().naive_utc()),
//              })
//     }

// }

/// Fetch the count of tasks and subtasks currently in the queue, excluding snoozed ones
// pub fn get_queue_count() -> (i64, i64) {
//     let connection = &mut establish_db_connection();

//     let now_text = sql::<Nullable<Text>>("strftime('%Y-%m-%d %H:%M:%S', 'now')");

//     let task_count: i64 = task_dsl::tasks
//         .filter(task_dsl::queue_position.is_not_null())
//         .filter(task_dsl::snoozed_until.is_null().or(task_dsl::snoozed_until.lt(now_text.clone())))
//         .count()
//         .get_result(connection)
//         .expect("Error counting tasks");

//     let subtask_count: i64 = subtask_dsl::subtasks
//         .filter(subtask_dsl::queue_position.is_not_null())
//         .filter(subtask_dsl::snoozed_until.is_null().or(subtask_dsl::snoozed_until.lt(now_text.clone())))
//         .count()
//         .get_result(connection)
//         .expect("Error counting subtasks");

//     (task_count, subtask_count)
// }
/// Mark a task or subtask as completed and remove it from the queue, tracking completion time
pub fn complete_task(task_id: i32, is_subtask: bool) {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    let completion_time = Utc::now().to_rfc3339();

    if is_subtask {
        diesel::update(subtask_dsl::subtasks.filter(subtask_dsl::id.eq(task_id)))
            .set((
                subtask_dsl::queue_position.eq::<Option<i32>>(None),
                subtask_dsl::status.eq("completed"),
                subtask_dsl::last_attempted_at.eq(completion_time),
            ))
            .execute(connection)
            .expect("Error marking subtask as completed");
    } else {
        diesel::update(task_dsl::tasks.filter(task_dsl::id.eq(task_id)))
            .set((
                task_dsl::queue_position.eq::<Option<i32>>(None),
                task_dsl::status.eq("completed"),
                task_dsl::last_attempted_at.eq(completion_time),
            ))
            .execute(connection)
            .expect("Error marking task as completed");
    }
}

/// Dequeue a task or subtask by removing it from the queue while keeping it active
pub fn dequeue_task(task_id: i32, is_subtask: bool) {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    if is_subtask {
        diesel::update(subtask_dsl::subtasks.filter(subtask_dsl::id.eq(task_id)))
            .set((
                subtask_dsl::queue_position.eq::<Option<i32>>(None),
                subtask_dsl::status.eq("pending"),
            ))
            .execute(connection)
            .expect("Error dequeuing subtask");
    } else {
        diesel::update(task_dsl::tasks.filter(task_dsl::id.eq(task_id)))
            .set((
                task_dsl::queue_position.eq::<Option<i32>>(None),
                task_dsl::status.eq("pending"),
            ))
            .execute(connection)
            .expect("Error dequeuing task");
    }
}

/// Assign a task or subtask to an available agent based on workload
pub fn assign_task_to_agent(task_id: i32, is_subtask: bool) {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    let (available_agent, assign_time) = if is_subtask {
        let available_agent: Option<String> = subtask_dsl::subtasks
            .select(subtask_dsl::assigned_to)
            .filter(subtask_dsl::assigned_to.is_not_null())
            .group_by(subtask_dsl::assigned_to)
            .order_by(diesel::dsl::count(subtask_dsl::assigned_to).asc())
            .first::<Option<String>>(connection) // Fix: Handle Nullable<Text> correctly
            .ok()
            .flatten(); // Fix: Converts Option<Option<String>> into Option<String>

        (available_agent, Utc::now().to_rfc3339())
    } else {
        let available_agent: Option<String> = task_dsl::tasks
            .select(task_dsl::assigned_to)
            .filter(task_dsl::assigned_to.is_not_null())
            .group_by(task_dsl::assigned_to)
            .order_by(diesel::dsl::count(task_dsl::assigned_to).asc())
            .first::<Option<String>>(connection) // Fix: Handle Nullable<Text> correctly
            .ok()
            .flatten(); // Fix: Converts Option<Option<String>> into Option<String>

        (available_agent, Utc::now().to_rfc3339())
    };

    if let Some(agent) = available_agent {
        if is_subtask {
            diesel::update(subtask_dsl::subtasks.filter(subtask_dsl::id.eq(task_id)))
                .set((
                    subtask_dsl::assigned_to.eq(agent),
                    subtask_dsl::last_attempted_at.eq(assign_time),
                ))
                .execute(connection)
                .expect("Error assigning subtask to agent");
        } else {
            diesel::update(task_dsl::tasks.filter(task_dsl::id.eq(task_id)))
                .set((
                    task_dsl::assigned_to.eq(agent),
                    task_dsl::last_attempted_at.eq(assign_time),
                ))
                .execute(connection)
                .expect("Error assigning task to agent");
        }
    }
}

/// Clear all completed tasks and subtasks from the database and track last cleanup time
pub fn clear_completed_tasks() {
    // let connection = &mut establish_db_connection();
    let connection = &mut get_pooled_connection();

    let cleanup_time = Utc::now().to_rfc3339();

    diesel::delete(task_dsl::tasks.filter(task_dsl::status.eq("completed")))
        .execute(connection)
        .expect("Error clearing completed tasks");

    diesel::delete(subtask_dsl::subtasks.filter(subtask_dsl::status.eq("completed")))
        .execute(connection)
        .expect("Error clearing completed subtasks");
}
